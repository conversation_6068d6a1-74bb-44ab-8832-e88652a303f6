<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>

    <Match>
        <!-- 接口类型强转实现类型，忽略不处理-->
        <!-- Write to static field from instance method-->
        <Package name="~.*"/>
        <Bug pattern="BC_BAD_CAST_TO_CONCRETE_COLLECTION"/>
    </Match>

    <Match>
        <!-- 实体赋值静态变量,一般初始化时候的操作，忽略不处理-->
        <!-- Write to static field from instance method-->
        <Package name="~.*"/>
        <Bug pattern="ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD"/>
    </Match>

    <Match>
        <!-- 自动装箱的问题，忽略不处理-->
        <!-- Boxing/unboxing to parse a primitive excute(Object)-->
        <Package name="~.*"/>
        <Bug pattern="DM_BOXED_PRIMITIVE_FOR_PARSING"/>
    </Match>
    <Match>
        <!--自动装箱的问题，忽略不处理 -->
        <!-- Primitive value is boxed then unboxed to perform primitive coercion-->
        <Package name="~.*"/>
        <Bug pattern="BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION"/>
    </Match>

    <Match>
        <!--装箱后拆箱紧接着装箱，忽略不处理 -->
        <!-- Boxed value is unboxed and then immediately reboxed-->
        <Package name="~.*"/>
        <Bug pattern="BX_UNBOXING_IMMEDIATELY_REBOXED"/>
    </Match>

    <Match>
        <!--过泛地捕获异常,将多个异常合并成一个父类异常捕获 ，忽略不处理-->
        <!-- Exception is caught when Exception is not thrown-->
        <Package name="~.*"/>
        <Bug pattern="REC_CATCH_EXCEPTION"/>
    </Match>
    <Match>
        <!--此代码包含文件对象为一个绝对路径名(路径硬编码)，忽略不处理-->
        <!-- Code contains a hard coded reference to an absolute pathname-->
        <Package name="~.*"/>
        <Bug pattern="DMI_HARDCODED_ABSOLUTE_FILENAME"/>
    </Match>

    <Match>
        <!--未明确指定编码格式，使用系统默认编码格式，忽略不处理-->
        <!-- Reliance on default encoding -->
        <Package name="~.*"/>
        <Bug pattern="DM_DEFAULT_ENCODING"/>
    </Match>

    <Match>
        <!--get方法获取的对象,存在可能被修改的问题，忽略不处理-->
        <!--May expose internal representation by returning reference to mutable object -->
        <Package name="~.*"/>
        <Bug pattern="EI_EXPOSE_REP"/>
    </Match>

    <Match>
        <!-- set方法设置的对象存在会被修改的问题，忽略不处理-->
        <!-- May expose internal representation by incorporating reference to mutable object-->
        <Package name="~.*"/>
        <Bug pattern="EI_EXPOSE_REP2"/>
    </Match>

</FindBugsFilter>