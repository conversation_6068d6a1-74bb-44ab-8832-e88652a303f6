package com.msun.core.component.implementation.api.msuncloud;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.failback.MsunCloudApiFallBack;
import com.msun.core.springcloud.api.annotation.api.MsunMapping;
import com.msun.core.springcloud.api.annotation.api.MsunPost;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/18 13:46
 */
@FeignClient(name = "msun-core-component-implementation-api", url = "default.chis.msunsoft.com",fallbackFactory = MsunCloudApiFallBack.class)
@Api(tags = "桂杰测试")
@MsunMapping("/api/gj")
public interface GuijieTest {
    @Data
    public static class TodoTaskCheckArgs {
        private Long hospitalId;
        private Long orgId;
        private String taskCode;
        private String surveyType;
        private String surveyResult;
    }

    /**
     * 代办任务检测测试
     * @param args
     * @return
     */
    @ApiOperation("代办任务检测测试")
    @ApiOperationSupport(author = "guijie")
    @MsunPost("/todoTaskTask")
    @ResponseBody
    ResponseResult<JSONObject> todoTaskTask(@RequestBody TodoTaskCheckArgs args);
}
