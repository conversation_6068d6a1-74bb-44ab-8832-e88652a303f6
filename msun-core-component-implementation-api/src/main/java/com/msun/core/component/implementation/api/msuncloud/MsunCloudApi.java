package com.msun.core.component.implementation.api.msuncloud;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.failback.MsunCloudApiFallBack;
import com.msun.core.springcloud.api.annotation.api.MsunMapping;
import com.msun.core.springcloud.api.annotation.api.MsunPost;
/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/18 13:40
 */
@FeignClient(name = "msun-core-component-implementation-api", url = "default.chis.msunsoft.com",fallbackFactory = MsunCloudApiFallBack.class)
@Api(tags = "调用云健康接口")
@MsunMapping("/api/msunCloudApi")
public interface MsunCloudApi {

    @Data
    public static class CallArgs {

        /**
         * TraceId
         */
        @ApiModelProperty("TraceId")
        private String traceId;
        /**
         * 客户ID
         */
        @NotNull
        @ApiModelProperty("客户ID")
        private Long hospitalId;

        /**
         * 机构id
         */
        @NotNull
        @ApiModelProperty("机构id")
        private Long hisOrgId;

        /**
         * 域名
         */
        @NotBlank
        @ApiModelProperty("domain")
        private String domain;

        /**
         * 运营平台用户id
         */
        @ApiModelProperty("运营平台用户id")
        private String yyUserId;

        /**
         * 运营平台用户用户账号
         */
        @ApiModelProperty("运营平台用户用户账号")
        private String yyUserAccount;

        /**
         * 运营平台用户用户名称
         */
        @ApiModelProperty("运营平台用户用户名称")
        private String yyUserName;

        /**
         * 服务名称
         */
        @NotBlank
        @ApiModelProperty("服务名称")
        private String serviceName;

        /**
         * 命名空间ID,默认：default
         */
        @ApiModelProperty("命名空间ID,默认：default")
        private String namespaceId = "default";

        /**
         * 集群名称,默认：DEFAULT
         */
        @ApiModelProperty("集群名称,默认：DEFAULT")
        private String clusterName = "DEFAULT";

        /**
         * 分组名,默认：DEFAULT_GROUP
         */
        @ApiModelProperty("分组名,默认：DEFAULT_GROUP")
        private String groupName = "DEFAULT_GROUP";

        /**
         * 接口URL
         */
        @NotBlank
        @ApiModelProperty("接口URL")
        private String apiUrl;

        /**
         * 接口方法：GET/POST/PUT/DELETE
         */
        @NotBlank
        @ApiModelProperty("接口方法：GET/POST/PUT/DELETE")
        private String apiMethod;

        /**
         * 接口请求头
         */
        @ApiModelProperty("接口请求头")
        private Map<String, String> apiHeaders;

        /**
         * 接口的url?拼接参数
         */
        @ApiModelProperty("接口的url?拼接参数")
        private Map<String, Object> apiQueryArgs;

        /**
         * 接口请求体body参数
         */
        @ApiModelProperty("接口请求体body参数")
        private String apiBodyArgs;
    }

    /**
     * 调用云健康接口
     * @param args
     * @return
     */
    @ApiOperation("调用云健康接口")
    @ApiOperationSupport(author = "guijie")
    @MsunPost("/callApi")
    @ResponseBody
    ResponseResult<?> callApi(@RequestBody @Valid CallArgs args);
}
