package com.msun.core.component.implementation.api.msuncloud;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.failback.MsunCloudDbFallBack;
import com.msun.core.springcloud.api.annotation.api.MsunMapping;
import com.msun.core.springcloud.api.annotation.api.MsunPost;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/27 14:07
 */
@FeignClient(name = "msun-core-component-implementation-api", url = "default.chis.msunsoft.com",fallbackFactory = MsunCloudDbFallBack.class)
@Api(tags = "执行云健康数据库脚本")
@MsunMapping("/api/msunCloudDb")
public interface MsunCloudDb {
    @Data
    public static class ExecSqlArgs {

        /**
         * TraceId
         */
        @ApiModelProperty("TraceId")
        private String traceId;
        /**
         * 客户ID
         */
        @NotNull
        @ApiModelProperty("客户ID")
        private Long hospitalId;

        /**
         * 机构id
         */
        @NotNull
        @ApiModelProperty("机构id")
        private Long hisOrgId;

        /**
         * 运营平台用户id
         */
        @ApiModelProperty("运营平台用户id")
        private String yyUserId;

        /**
         * 运营平台用户用户账号
         */
        @ApiModelProperty("运营平台用户用户账号")
        private String yyUserAccount;

        /**
         * 运营平台用户用户名称
         */
        @ApiModelProperty("运营平台用户用户名称")
        private String yyUserName;

        @NotBlank
        @ApiModelProperty("数据库名称chis、chisapp")
        private String dbName;

        @NotBlank
        @ApiModelProperty("aes加密后的SQL语句")
        private String aesEnSql;

        @ApiModelProperty("是否使用流式查询，默认true")
        private Boolean byStreamQuery = true;
    }

    /**
     * 执行查询语句
     * @param args
     * @return
     */
    @ApiOperation("执行查询语句")
    @ApiOperationSupport(author = "guijie")
    @MsunPost("/execQuery")
    @ResponseBody
    ResponseResult<List<JSONObject>> execQuery(@RequestBody ExecSqlArgs args);
}
