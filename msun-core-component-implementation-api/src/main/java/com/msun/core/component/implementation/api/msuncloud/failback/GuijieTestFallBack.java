package com.msun.core.component.implementation.api.msuncloud.failback;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.GuijieTest;

/**
 * @description: TODO
 *
 * <AUTHOR> 7630
 * @date 2025/8/27 19:50
 */
@Slf4j
@Component
public class GuijieTestFallBack implements FallbackFactory<GuijieTest> {
    /**
     * @param throwable
     * @return
     */
    @Override
    public GuijieTest create(Throwable throwable) {
        return new GuijieTest() {
            @Override
            public ResponseResult<JSONObject> todoTaskTask(TodoTaskCheckArgs args) {
                return null;
            }
        };
    }
}
