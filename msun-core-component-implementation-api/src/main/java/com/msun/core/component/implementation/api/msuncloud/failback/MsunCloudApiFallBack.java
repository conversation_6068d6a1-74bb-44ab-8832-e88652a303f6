package com.msun.core.component.implementation.api.msuncloud.failback;

import cn.hutool.core.util.StrUtil;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.MsunCloudApi;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/18 14:44
 */
@Slf4j
@Component
public class MsunCloudApiFallBack implements FallbackFactory<MsunCloudApi> {
    @Override
    public MsunCloudApi create(Throwable throwable) {
        return new MsunCloudApi() {
            @Override
            public ResponseResult<?> callApi(CallArgs args) {
                log.warn("traceId({}) - 用户【{}#{}】执行云健康接口报错：{}，参数：{}", args.getTraceId(), args.getYyUserId(), args.getYyUserAccount(), throwable.getMessage(), JSON.toJSONString(args), throwable);
                throw new RuntimeException(StrUtil.format("traceId({}) - 用户【{}#{}】执行云健康接口报错：{}，参数：{}", args.getTraceId(), args.getYyUserId(), args.getYyUserAccount(), throwable.getMessage(), JSON.toJSONString(args)), throwable);
            }
        };
    }
}
