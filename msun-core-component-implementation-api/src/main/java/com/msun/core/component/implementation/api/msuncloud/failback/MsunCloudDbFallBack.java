package com.msun.core.component.implementation.api.msuncloud.failback;

import cn.hutool.core.util.StrUtil;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.MsunCloudDb;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/27 14:01
 */
@Slf4j
@Component
public class MsunCloudDbFallBack implements FallbackFactory<MsunCloudDb> {
    @Override
    public MsunCloudDb create(Throwable cause) {
        return new MsunCloudDb() {
            /**
             * 执行云健康查询语句
             * @param args
             * @return
             */
            @Override
            public ResponseResult<List<JSONObject>> execQuery(ExecSqlArgs args) {
                log.error("traceId({}) - 用户【{}#{}】执行云健康查询语句失败: {}，参数：{}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), cause.getMessage(), JSON.toJSONString(args), cause);
                throw new RuntimeException(StrUtil.format("traceId({}) - 用户【{}#{}】执行云健康查询语句失败: {}，参数：{}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), cause.getMessage(), JSON.toJSONString(args)), cause);
            }
        };
    }
}
