#! /bin/sh
# 变量构造
HOME_DIR=/home/<USER>
PROJECT_GROUP=msun-his-base
PROJECT_NAME=msun-his-base-common-server
PROFILE=$1
DEPLOY_DIR=$HOME_DIR/deploy/$PROJECT_NAME
ZIP_FILE_NAME=$PROJECT_NAME.zip
ZIP_FILE=$DEPLOY_DIR/target/$ZIP_FILE_NAME
TARGET_PATH=$HOME_DIR/$PROJECT_GROUP/$PROJECT_NAME

# 日志输出开始
echo "PROJECT_GROUP="$PROJECT_GROUP
echo "PROJECT_NAME="$PROJECT_NAME
echo "PROFILE="$PROFILE
echo "ZIP_FILE_NAME="$ZIP_FILE_NAME
echo "ZIP_FILE="$ZIP_FILE
echo "TARGET_PATH="$TARGET_PATH

#文件操作
mkdir -p $TARGET_PATH/logs
cd $TARGET_PATH
sh kill.sh
echo "KILL SUCCESS"
rm -rf $(ls | grep -v logs)
echo "DELETE OLD FILES(EXCLUDE logs),DIR="$(ls | grep -v logs)
cp $ZIP_FILE $TARGET_PATH
unzip -o $ZIP_FILE_NAME
sh startup.sh $PROFILE
echo "STARTUP SUCCESS"
#启动日志打印
timeout 3 echo "wait for print log"
timeout 30 tail -f $TARGET_PATH/logs/o2o.log