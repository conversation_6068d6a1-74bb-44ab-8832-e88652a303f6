#!/usr/bin/env bash

#当前工作目录
CURRENT_DICT=`dirname $0`
if [ "$CURRENT_DICT" == "." ]; then
CURRENT_DICT=`pwd`
fi
echo CURRENT_DICT=${CURRENT_DICT}
#项目名
PROJECT_NAME=${CURRENT_DICT##*/}
echo PROJECT_NAME=${PROJECT_NAME}

pid=$(ps -ef|grep java|grep $PROJECT_NAME  |awk '{print $2}')
echo $pid
while [  -n "$pid" ]; do
    echo "kill $pid"
    kill $pid
    sleep 1s
pid=$(ps -ef|grep java|grep $PROJECT_NAME  |awk '{print $2}')
done
