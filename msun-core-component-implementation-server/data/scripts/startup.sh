#! /bin/sh
#export LOG_PATH="/msun/logs"
#export SHARE_JAVA_OPTION="-Xmx2048m -Xms2048m -Xmn1024m"
#export msun_his_base_sso_server_vm="-Xmx1024m -Xms1024m -Xmn512m"
#设置主程序主路径（手动修改），个性化配置环境变量=当前文件夹名称_vm
APP_MAIN="com.msun.core.component.implementation.server.MsunCoreComponentImplementationApplication"
echo APP_MAIN=${APP_MAIN}

#当前工作目录
CURRENT_DICT=`dirname $0`
if [ "$CURRENT_DICT" == "." ]; then
CURRENT_DICT=`pwd`
fi
echo CURRENT_DICT=${CURRENT_DICT}
#加载环境变量
source /etc/profile
#项目名
PROJECT_NAME=${CURRENT_DICT##*/}
PROJECT_LINE_NAME=${PROJECT_NAME//-/_}
echo PROJECT_NAME=${PROJECT_NAME}
#判断是否启动过
PID=$(ps -ef|grep java|grep $PROJECT_NAME  |awk '{print $2}')
if [ "${PID}" != "" ]; then
    echo "Service Have StartUp !!!"
    exit 0;
fi

#日志目录
LOG=${CURRENT_DICT}
if [ "${LOG_PATH}" != "" ]; then
LOG=${LOG_PATH}/${PROJECT_NAME}
fi
mkdir -p ${LOG}
CONSOLE_LOG=${LOG}"/console.log"
echo CONSOLE_LOG=${CONSOLE_LOG}
#Lib路径
LIBDIRS=${CURRENT_DICT}/lib
#设置ClassPath
CLASSPATH=.:${CURRENT_DICT}/config
echo CLASSPATH=${CLASSPATH}
for JARS in `ls ${LIBDIRS}`
do
CLASSPATH=${CLASSPATH}:${LIBDIRS}/${JARS}
done
echo CLASSPATH=${CLASSPATH}
#设置时区
TIME_ZONE=${DEFAULT_TIME_ZONE:="-Duser.timezone=Asia/Shanghai"}
echo TIME_ZONE=${TIME_ZONE}
#设置oom路径
OOM="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOG}/oom.log"
echo OOM=${OOM}
#设置内存配置
#读取公共内存设置
SHARE_JAVA_OPTION=${SHARE_JAVA_OPTION:="-Xmx512m -Xms512m -Xmn256m"}
#读取每个应用自己设置的内存
CUSTOM_JAVA_OPTION_NAME=${PROJECT_LINE_NAME}"_vm"
eval CUSTOM_JAVA_OPTION='$'"$CUSTOM_JAVA_OPTION_NAME"
JAVA_OPTION=${SHARE_JAVA_OPTION}
if [ "$CUSTOM_JAVA_OPTION" != "" ]; then
JAVA_OPTION=${CUSTOM_JAVA_OPTION}
fi
echo JAVA_OPTION=${JAVA_OPTION}
#设置通用自定义配置执行
COMMON_OPTION=${COMMON_OPTION:=""}
echo COMMON_OPTION=${COMMON_OPTION}
#组装执行命令
JAVA_OPT="${JAVA_OPTION} ${COMMON_OPTION} ${OOM} ${TIME_ZONE}"
echo JAVA_OPT=${JAVA_OPT}
#执行
java ${JAVA_OPT} -cp ${CLASSPATH} ${APP_MAIN} >> ${CONSOLE_LOG} &
