<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>msun-core-component-implementation</artifactId>
        <groupId>com.msun.core.component.implementation</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>msun-core-component-implementation-server</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.msun.core.springcloud.server</groupId>
            <artifactId>msun-springcloud-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.msun.core.db</groupId>
            <artifactId>msun-db</artifactId>
        </dependency>

        <dependency>
            <groupId>com.msun.core.mq</groupId>
            <artifactId>msun-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.msun.core.component.implementation</groupId>
            <artifactId>msun-core-component-implementation-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.6.2</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>cn.hutool</groupId>-->
<!--            <artifactId>hutool-all</artifactId>-->
<!--            <version>5.6.2</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>io.github.biezhi</groupId>-->
<!--            <artifactId>TinyPinyin</artifactId>-->
<!--            <version>2.0.3.RELEASE</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.github.stuxuhai</groupId>-->
<!--            <artifactId>jpinyin</artifactId>-->
<!--            <version>1.1.8</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.8.11.2</version>
        </dependency>
        <dependency>
            <groupId>com.msun.core.component.implementation</groupId>
            <artifactId>msun-core-component-implementation-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!--        <dependency>-->
<!--            <groupId>com.msun.middle.base</groupId>-->
<!--            <artifactId>msun-middle-base-dict-api</artifactId>-->
<!--            <version>0.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.msun.middle.base</groupId>-->
<!--            <artifactId>msun-middle-base-resource-api</artifactId>-->
<!--            <version>0.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->
    </dependencies>

    <profiles>
        <profile>
            <id>build-to-zip</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <finalName>${project.artifactId}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <excludes>
                                <exclude>*.properties</exclude>
                                <exclude>*.xml</exclude>
                                <exclude>*.yml</exclude>
                                <exclude>config/*.yml</exclude>
                                <exclude>config/*.xml</exclude>
                                <exclude>config/*.properties</exclude>
                                <exclude>mybatis/*.xml</exclude>
                                <exclude>mybatis/*/*.xml</exclude>
                                <exclude>mybatis/*/*/*.xml</exclude>
                                <exclude>mybatis/*/*/*/*.xml</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>build-to-jar</id>
            <build>
                <finalName>${project.artifactId}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
