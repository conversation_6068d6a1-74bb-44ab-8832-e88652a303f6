<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <formats>
        <format>zip</format>
    </formats>
    <!--不包含当前项目的<artifactId>opnc-eureka</artifactId>指定的opnc-eureka -->
    <includeBaseDirectory>false</includeBaseDirectory>

    <dependencySets>
        <dependencySet>
            <unpack>false</unpack>
            <outputDirectory>lib</outputDirectory>
            <useTransitiveFiltering>true</useTransitiveFiltering>
            <useStrictFiltering>true</useStrictFiltering>
            <fileMode>666</fileMode>
        </dependencySet>
    </dependencySets>


    <fileSets>
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
                <include>**</include>
            </includes>
            <excludes>
                <exclude>pics/*</exclude>
            </excludes>
            <filtered>true</filtered>
            <outputDirectory>${file.separator}</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/resources/pics</directory>
            <filtered>false</filtered>
            <outputDirectory>${file.separator}/pics</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/data/scripts</directory>
            <fileMode>766</fileMode>
            <includes>
                <include>isAlive.sh</include>
                <include>kill.sh</include>
                <include>startup.sh</include>
            </includes>
            <outputDirectory>${file.separator}</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>