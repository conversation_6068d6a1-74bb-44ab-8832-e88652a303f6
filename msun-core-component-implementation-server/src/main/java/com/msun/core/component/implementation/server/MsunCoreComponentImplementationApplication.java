package com.msun.core.component.implementation.server;

import com.msun.core.component.implementation.MsunCoreComponentImplementationAutoConfigration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @description:
 * @fileName: MsunDeviceAnaysisLocalApplication.java
 * @author: jason
 * @createAt: 2021/1/25 2:40 下午
 * @updateBy: jason
 * @remark: Copyright
 */
@Slf4j
@SpringBootApplication(exclude = {MsunCoreComponentImplementationAutoConfigration.class})
@EnableFeignClients
@EnableDiscoveryClient
public class MsunCoreComponentImplementationApplication {

    public static void main(String[] args) {
        System.out.println("");
        SpringApplication.
                run(MsunCoreComponentImplementationApplication.class,args);
        log.warn("实施平台接口----启动完毕");
    }

}
