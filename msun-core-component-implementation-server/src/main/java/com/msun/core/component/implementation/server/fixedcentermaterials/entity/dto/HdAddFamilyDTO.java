package com.msun.core.component.implementation.server.fixedcentermaterials.entity.dto;

import com.msun.core.commons.api.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 家庭数据
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Getter
@Setter
@ToString
@ApiModel(description = "家庭数据")
public class HdAddFamilyDTO extends BaseDTO {

    /**
     * 交付平台该数据唯一标识，用于回更上传状态
     */
    @ApiModelProperty(value = "交付平台该数据唯一标识，用于回更上传状态")
    private String id;

    /**
     * 户主姓名
     */
    @ApiModelProperty(value = "户主姓名")
    private String name;

    /**
     * 家庭档案实体转json，实体见附件
     */
    @ApiModelProperty(value = "家庭档案实体转json，实体见附件")
    private String json;

    /**
     * 交付医院id
     */
    @ApiModelProperty(value = "交付医院id")
    private Long hospitalId;

    /**
     * 交付环境ID
     */
    @ApiModelProperty(value = "交付环境ID")
    private Long hisOrgId;
}
