package com.msun.core.component.implementation.server.fixedcentermaterials.entity.vo;

import com.msun.core.commons.api.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 家庭数据
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Getter
@Setter
@ToString
@ApiModel(description = "家庭数据")
public class HdAddFamilyResultVO extends BaseDTO {
    /**
     * 入参唯一标识（原封返回，用于更新交付上传状态）
     */
    @ApiModelProperty(value = "入参唯一标识（原封返回，用于更新交付上传状态）")
    private String id;

    /**
     * 户主姓名（原封返回，用于更新交付上传状态）
     */
    @ApiModelProperty(value = "户主姓名（原封返回，用于更新交付上传状态）")
    private String name;

    /**
     * 成功标识
     */
    @ApiModelProperty(value = "成功标识")
    private Boolean success;

    /**
     * 该条数据保存失败原因
     */
    @ApiModelProperty(value = "该条数据保存失败原因")
    private String msg;

}
