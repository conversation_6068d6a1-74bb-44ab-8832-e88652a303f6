package com.msun.core.component.implementation.server.fixedcentermaterials.feign;

import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.fixedcentermaterials.dto.DeviceCardsListDTO;
import com.msun.core.component.implementation.api.fixedcentermaterials.dto.DeviceContentDTO;
import com.msun.core.component.implementation.server.fixedcentermaterials.feign.fallback.DeviceCenterContentFallBackImpl;
import com.msun.core.springcloud.api.annotation.api.MsunMapping;
import com.msun.core.springcloud.api.annotation.api.MsunPost;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "msun-middle-business-device-server", fallbackFactory = DeviceCenterContentFallBackImpl.class)
@Api(tags = "调用设备中台接口")
@MsunMapping("/api/deviceApi")
public interface DeviceCenterContentFeign {
    /**
     * 批量插入设备中台数据
     *
     * @param batchInsertDTO
     * @return
     */
    @ApiOperation("批量插入设备中台数据")
    @MsunPost("/saveDeviceCards")
    Object saveDeviceCards(DeviceCardsListDTO<DeviceContentDTO> batchInsertDTO);
}
