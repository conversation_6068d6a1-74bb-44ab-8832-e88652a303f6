package com.msun.core.component.implementation.server.fixedcentermaterials.service;

import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.fixedcentermaterials.dto.*;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertDTO;
import com.msun.core.component.implementation.api.imsp.dto.ParamSqlDTO;
import com.msun.core.component.implementation.api.publichealth.dto.HdResultOutDto;

import java.util.Map;

/**
 * @classDesc: 功能描述:实施平台患者相关表数据导入的服务层
 * @author: 王军
 * @date: 2021/3/12 8:57
 * @copyright 众阳健康
 */
public interface FixedCenterMaterialsService {


    /**
     * 批量插入固定资产数据
     *
     * @param batchInsertDTO
     * @return
     */
    Object fixedAssetsContentInsert(EquipmentCardsListDTO<DeviceContentDTO> batchInsertDTO);
    /**
     * 获取固定资产总数量
     * @param batchInsert
     * @return
     */
    Integer getFixedAssetsCount(BatchInsertDTO batchInsert);
    /**
     * 批量插入设备中台数据
     *
     * @param batchInsertDTO
     * @return
     */
    Object deviceCenterContentInsert(DeviceCardsListDTO<DeviceContentDTO> batchInsertDTO);
    /**
     * 获取设备中台总数量
     * @param batchInsert
     * @return
     */
    Integer getDeviceCenterCount(BatchInsertDTO batchInsert);
    /**
     * 批量插入后勤物资数据
     *
     * @param batchInsertDTO
     * @return
     */
    Object logisticsMaterialsInsert(DictAndStockExcelImspDto<DictAndStockExcelDto> batchInsertDTO);
    /**
     * 获取后勤物资总数量
     * @param batchInsert
     * @return
     */
    Integer getLogisticsMaterialsCount(BatchInsertDTO<ParamSqlDTO> batchInsert);
    /**
     * 通过脚本获取额定信息
     * @param batchInsert
     * @return
     */
    String getDataCount(BatchInsertDTO<ParamSqlDTO> batchInsert);
}
