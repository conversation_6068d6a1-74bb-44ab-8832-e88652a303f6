package com.msun.core.component.implementation.server.fixedcentermaterials.service.impl;

import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.error.BaseErrorCode;
import com.msun.core.component.implementation.api.fixedcentermaterials.dto.*;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertDTO;
import com.msun.core.component.implementation.api.imsp.dto.ParamSqlDTO;
import com.msun.core.component.implementation.api.imsp.vo.ImspReturnVO;
import com.msun.core.component.implementation.api.publichealth.dto.HdResultOutDto;
import com.msun.core.component.implementation.server.fixchargeconfig.enums.ParamSqlEnum;
import com.msun.core.component.implementation.server.fixedcentermaterials.feign.DeviceCenterContentFeign;
import com.msun.core.component.implementation.server.fixedcentermaterials.feign.FixedAssetsContentFeign;
import com.msun.core.component.implementation.server.fixedcentermaterials.feign.LogisticsMaterialsFeign;
import com.msun.core.component.implementation.server.fixedcentermaterials.service.FixedCenterMaterialsService;
import com.msun.core.component.implementation.server.imsp.dao.ImspPullMapper;
import com.msun.core.component.implementation.server.publichealth.dao.HdPersonalBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description 患者数据导入实现类
 * @ClassName: PatRelateImpServiceImp
 * @author: wangj
 * @Date: 2021/3/12 9:16
 */
@Slf4j
@Service
public class FixedCenterMaterialsServiceImpl implements FixedCenterMaterialsService {
    @Resource
    private DeviceCenterContentFeign deviceCenterContentFeign;

    @Resource
    private FixedAssetsContentFeign fixedAssetsContentFeign;
    @Resource
    private LogisticsMaterialsFeign logisticsMaterialsFeign;

    @Resource
    private ImspPullMapper imspPullMapper;
    @Resource
    private HdPersonalBaseMapper hdPersonalBaseMapper;
    /**
     * 批量插入固定资产数据
     *
     * @param batchInsertDTO
     * @return
     */
    @Override
    public  Object  fixedAssetsContentInsert(EquipmentCardsListDTO<DeviceContentDTO> batchInsertDTO) {
//        ImspReturnVO imspReturn = new ImspReturnVO();
//        imspReturn.setPasse(0);
//        List<DeviceContentDTO> hdFamilyArchivesDeliveryDTOList =  batchInsertDTO.getData();
//        HdResultOutDto<Map<String, Object>>

        //调用公卫家庭档案接口
        Object listResult =null;
        try{
            log.info("调用固定资产接口---------------------");
            listResult =  fixedAssetsContentFeign.importEquipmentCards(batchInsertDTO);
            log.info("调用固定资产接口接口返回结果--------------------",listResult);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseResult.error(BaseErrorCode.FALLBACK, "系统异常：==" + e.getMessage());
        }
        return listResult;
    }


    /**
     * 后勤物资
     *
     * @param batchInsertDTO
     * @return
     */
    @Override
    public  Object  logisticsMaterialsInsert(DictAndStockExcelImspDto<DictAndStockExcelDto> batchInsertDTO) {
//        ImspReturnVO imspReturn = new ImspReturnVO();
//        imspReturn.setPasse(0);
//        List<DictAndStockExcelDto> dictAndStockExcelDtos =  batchInsertDTO.getData();
        //调用公卫家庭档案接口
        Object listResult =null;
        try{
            log.info("调用后勤物资接口---------------------");
            listResult =  logisticsMaterialsFeign.importMaterialForImsp(batchInsertDTO);
            log.info("调用后勤物资接口接口返回结果---------------------",listResult);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseResult.error(BaseErrorCode.FALLBACK, "系统异常：==" + e.getMessage());
        }
        return listResult;
    }

    /**
     * 设备中台
     *
     * @param batchInsertDTO
     * @return
     */
    @Override
    public  Object  deviceCenterContentInsert(DeviceCardsListDTO<DeviceContentDTO> batchInsertDTO) {
//        ImspReturnVO imspReturn = new ImspReturnVO();
//        imspReturn.setPasse(0);
//        List<DeviceContentDTO> hdFamilyArchivesDeliveryDTOList =  batchInsertDTO.getData();
        //调用公卫家庭档案接口
        Object listResult =null;
        try{
            log.info("调用设备中台接口---------------------");
            listResult =  deviceCenterContentFeign.saveDeviceCards(batchInsertDTO);
            log.info("调用设备中台接口接口返回结果---------------------",listResult);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseResult.error(BaseErrorCode.FALLBACK, "系统异常：==" + e.getMessage());
        }
        return listResult;
    }
    /**
     * 获取后勤物资总数量
     * @param batchInsert
     * @return
     */
    @Override
    public Integer getLogisticsMaterialsCount(BatchInsertDTO<ParamSqlDTO> batchInsert) {
        int total;
        List<ParamSqlDTO> list = batchInsert.getData();
        ParamSqlDTO paramSqlDTO = list.get(0);
        if (ParamSqlEnum.SQL_CHIS.getDesc().equals(paramSqlDTO.getSqlType())){
            total = imspPullMapper.getChisDataCount(paramSqlDTO.getSql());
        }else{
            total = hdPersonalBaseMapper.getChisappDataCount(paramSqlDTO.getSql());
        }
        return total;
    }
    /**
     * 获取固定资产总数量
     * @param batchInsert
     * @return
     */
    @Override
    public Integer getFixedAssetsCount(BatchInsertDTO batchInsert) {
        log.info("batchInsert:{}",batchInsert);
        int total = hdPersonalBaseMapper.getFaCardsCount(batchInsert.getHospitalId());
        return total;
    }
    /**
     * 获取设备中台总数量
     * @param batchInsert
     * @return
     */
    @Override
    public Integer getDeviceCenterCount(BatchInsertDTO batchInsert) {
        int total = imspPullMapper.getFaDeviceCardsCount(batchInsert.getHospitalId());
        return total;
    }

    /**
     * 通过脚本获取额定信息
     * @param batchInsert
     * @return
     */
    @Override
    public String getDataCount(BatchInsertDTO<ParamSqlDTO> batchInsert) {
        String total;
        List<ParamSqlDTO> list = batchInsert.getData();
        ParamSqlDTO paramSqlDTO = list.get(0);
        if (ParamSqlEnum.SQL_CHIS.getDesc().equals(paramSqlDTO.getSqlType())){
            total = imspPullMapper.getChisStringData(paramSqlDTO.getSql());
        }else{
            total = hdPersonalBaseMapper.getChisappStringData(paramSqlDTO.getSql());
        }
        return total;
    }
}
