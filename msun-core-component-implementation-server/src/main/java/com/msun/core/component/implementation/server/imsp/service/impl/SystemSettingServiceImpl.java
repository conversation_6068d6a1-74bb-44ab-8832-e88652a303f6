package com.msun.core.component.implementation.server.imsp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.core.commons.error.BaseErrorCode.FALLBACK;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.context.LoginUserContext;
import com.msun.core.commons.context.LoginUserInfo;
import com.msun.core.component.implementation.api.imsp.dto.AppLoginDTO;
import com.msun.core.component.implementation.api.imsp.dto.AvailableLabQueryDTO;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertGeneralDTO;
import com.msun.core.component.implementation.api.imsp.dto.ChangeHospitalDto;
import com.msun.core.component.implementation.api.imsp.dto.DataTransDto;
import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentCompareWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusDto;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.GetAllAppDto;
import com.msun.core.component.implementation.api.imsp.dto.LisEquipmentItemDTO;
import com.msun.core.component.implementation.api.imsp.dto.OldNewEquipmentCompareDto;
import com.msun.core.component.implementation.api.imsp.dto.ProductAuthReqDTO;
import com.msun.core.component.implementation.api.imsp.dto.ProductAuthResultDTO;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentWrapperDto;
import com.msun.core.component.implementation.api.imsp.dto.SendMessageValueDto;
import com.msun.core.component.implementation.api.imsp.dto.SendSurveyValueDto;
import com.msun.core.component.implementation.api.imsp.dto.SurveyValueAndSurveyContent;
import com.msun.core.component.implementation.api.imsp.dto.SurveyValueDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemAuthorizeInfoSaveDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.api.imsp.dto.VerifyEquipmentStatusDto;
import com.msun.core.component.implementation.api.imsp.vo.HisLoginVO;
import com.msun.core.component.implementation.api.imsp.vo.ImspUpdateProductResultVo;
import com.msun.core.component.implementation.api.imsp.vo.ProductEncryptDto;
import com.msun.core.component.implementation.api.imsp.vo.SystemSettingResultDto;
import com.msun.core.component.implementation.server.aims.fegin.ApplyEquipmentInfoApi;
import com.msun.core.component.implementation.server.csm.feign.CsmDataToApi;
import com.msun.core.component.implementation.server.deviceanaysis.feign.LisBaseDataApi;
import com.msun.core.component.implementation.server.dictsurgeries.service.PacsEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.BbkProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.BbkTwoProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.DataTransApplicationFeign;
import com.msun.core.component.implementation.server.drug.feign.EcgProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.HdJfProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.PacsProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.PathProductEquipmentFeign;
import com.msun.core.component.implementation.server.drug.feign.ProductEncryptionFeign;
import com.msun.core.component.implementation.server.drug.feign.SystemAccountFeign;
import com.msun.core.component.implementation.server.drug.feign.SystemGetAllAppFeign;
import com.msun.core.component.implementation.server.drug.feign.SystemLoginFeign;
import com.msun.core.component.implementation.server.drug.feign.SystemMessageFeign;
import com.msun.core.component.implementation.server.drug.feign.SystemSettingFeign;
import com.msun.core.component.implementation.server.icuis.feign.IcuisEquipControllerApi;
import com.msun.core.component.implementation.server.imsp.dao.SystemSettingScriptMapper;
import com.msun.core.component.implementation.server.imsp.service.SystemSettingService;

/**
 * <AUTHOR>
 * @data 2022-03-28 11:08
 */
@Slf4j
@Service
public class SystemSettingServiceImpl implements SystemSettingService {

    @Value("${csm.publicKey}")
    private String publicKey;
    @Value("${csm.appId}")
    private String appId;

    @Resource
    ProductEncryptionFeign productEncryptionFeign;
    @Resource
    PacsProductEquipmentFeign pacsProductEquipmentFeign;
    @Resource
    EcgProductEquipmentFeign ccgProductEquipmentFeign;
    @Resource
    PathProductEquipmentFeign pathProductEquipmentFeign;
    @Resource
    BbkProductEquipmentFeign bbkProductEquipmentFeign;
    @Resource
    BbkTwoProductEquipmentFeign bbkTwoProductEquipmentFeign;
    @Resource
    HdJfProductEquipmentFeign hdJfProductEquipmentFeign;
    @Resource
    ApplyEquipmentInfoApi applyEquipmentInfoApi;
    @Resource
    IcuisEquipControllerApi icuisEquipControllerApi;
    @Resource
    private SystemSettingFeign systemSettingFeign;
    @Resource
    private SystemLoginFeign systemLoginFeign;
    @Resource
    private SystemGetAllAppFeign systemGetAllAppFeign;
    @Resource
    private PacsEquipmentFeign pacsEquipmentFeign;
    @Resource
    private SystemMessageFeign systemMessageFeign;
    @Resource
    private LisBaseDataApi lisBaseDataApi;
    @Resource
    private SystemAccountFeign systemAccountFeign;

    @Lazy
    @Resource
    private DataTransApplicationFeign dataTransApplicationFeign;

    @Resource
    private SystemSettingScriptMapper systemSettingScriptMapper;

    @Resource
    private CsmDataToApi csmDataToApi;

    private final static Integer TWO = 2;

    @Override
    public ResponseResult sendHisData(SystemConfigDto<SurveyValueAndSurveyContent> dto) {
        log.info("his接口,接收到调研生成配置的数据了......");
        log.info("SystemSettingApi:接口接受的数据" + dto.toString());
        //调用接口需要调整的参数
        SendSurveyValueDto sendSurveyValueDto = new SendSurveyValueDto();
        //HospitalId
        sendSurveyValueDto.setHospitalId(dto.getHospitalId());
        //交付平台固定写死
        sendSurveyValueDto.setOperateOrigin("implement_platform");
        //机构ID
        sendSurveyValueDto.setHisOrgId(dto.getHisOrgId());
        //配置数据
        List<SurveyValueAndSurveyContent> datas = dto.getData();
        //需要组装的新DTO
        List<SurveyValueDto> newDatas = null;
        SurveyValueDto newDto = null;
        if (null != datas && datas.size() > 0) {
            newDatas = new ArrayList<>();
            for (SurveyValueAndSurveyContent da : datas) {
                newDto = new SurveyValueDto();
                //配置编码
                newDto.setConfigCode(da.getConfigNumber());
                //配置值
                newDto.setConfigValue(da.getItemValue());
                //配置类型
                newDto.setConfigType(da.getConfigType());
                //配置ID
//                newDto.setConfigId(da.getId());
                //添加数据
                newDatas.add(newDto);
            }

        }
        //设置需要发送的数据
        sendSurveyValueDto.setHosScopeItemList(newDatas);
        log.info("开始调用指定配置接口...");
        //调用接口发送数据
        log.info("调用发送的数据: " + sendSurveyValueDto);
        ResponseResult result = systemSettingFeign.operateHospitalScope(sendSurveyValueDto);
        log.info("调用his接口返回结果:" + result);
        //解析返回值结果
        return result;
    }

    @Override
    public ResponseResult sentProjectEncryption(SystemConfigDto<SystemAuthorizeInfoSaveDto> dto) {
        ResponseResult responseResult = null;
        //log.info("向基础中台发送数据 接收的请求参数为:" + dto);
        //发送数据前 数据重新组装
        List<SystemAuthorizeInfoSaveDto> data = dto.getData();
        for (SystemAuthorizeInfoSaveDto item : data) {
            item.setHisOrgId(dto.getHisOrgId());
            item.setOrgId(dto.getHisOrgId());
            item.setHospitalId(dto.getHospitalId());
        }
        //log.info("调用基础中台发送的数据..." + data);
        responseResult = productEncryptionFeign.sentProjectEncryption(data);
        //log.info("基础中台返回的数据: " + responseResult.toString());
        return responseResult;


    }

    @Override
    public ResponseResult sentProjectEncryptionBefore(SystemConfigDto<SystemAuthorizeInfoSaveDto> dto) {
        ResponseResult responseResult = null;
        log.info("向基础中台发送加密数据前 查询父模块 接收的请求参数为:" + dto);
        //发送数据前 数据重新组装
        List<SystemAuthorizeInfoSaveDto> data = dto.getData();
        for (SystemAuthorizeInfoSaveDto item : data) {
            item.setHisOrgId(dto.getHisOrgId());
            item.setOrgId(dto.getHisOrgId());
            item.setHospitalId(dto.getHospitalId());
        }
        log.info("调用基础中台发送的数据...查询父模块" + data);
        responseResult = productEncryptionFeign.sentProjectEncryptionBefore(data);
        log.info("基础中台返回的数据 查询父模块: " + responseResult.toString());
        return responseResult;

    }

    @Override
    public ResponseResult syncEquipment(List<ProductEquipmentDto> dto) {
        log.info("交付平台发送硬件设备调研数据日志信息:{}", dto);
        ResponseResult rs = new ResponseResult();
        rs.setCode("200");
        //根据产品code判断 调用各产品的接口
        for (ProductEquipmentDto d : dto) {
            try {
                switch (d.getProductCode()) {
                    //PACS
                    case "PACS":
                        //调用
                        rs = pacsProductEquipmentFeign.sentProductEnquipmentData(d);
                        break;
                    //心电
                    case "ECG":
                        rs = ccgProductEquipmentFeign.sentProductEnquipmentData(d);
                        break;
                    //病理
                    case "PATH":
                        rs = pathProductEquipmentFeign.sentProductEnquipmentData(d);
                        break;
                    //血库
                    case "BBK":
                        rs = bbkProductEquipmentFeign.sentProductEnquipmentData(d);
                        break;
                    //血透
                    case "HD":
                        rs = hdJfProductEquipmentFeign.sentProductEnquipmentData(d);
                        break;
                    //手麻
                    case "AIMS":
                        log.info("调用手麻发送接口:{}", d);
                        rs = applyEquipmentInfoApi.sentProductEnquipmentData(d);
                        break;
                    //重症
                    case "ICU":
                        log.info("调用重症发送接口:{}", d);
                        rs = icuisEquipControllerApi.sentProductEnquipmentData(d);
                        break;
                    default:
                        break;
                }
                //发送成功
                d.setSentStatus("1");
                log.info("调研接口返回结果信息:{}", rs);
            } catch (Exception e) {
                e.printStackTrace();
                //发送失败
                d.setSentStatus("0");
            }
        }
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(true);
        responseResult.setData(dto);
        return responseResult;
    }

    @Override
    public ResponseResult<ImspUpdateProductResultVo> imspUpdateProductEquip(ProductEquipmentDto dto) {
        try {
            //根据产品Code 判断修改的设备信息为哪个产品，调用不同接口
            log.info("交付修改云健康产品设备数据【参数信息】，============= , {}", JSONUtil.toJsonStr(dto));
            ResponseResult result = new ResponseResult();
            ImspUpdateProductResultVo imspUpdateProductResultVo = new ImspUpdateProductResultVo();
            switch (dto.getProductCode()) {
                //PACS
                case "PACS":
                    //调用
                    result = pacsProductEquipmentFeign.updateEquipment(dto);
                    break;
                //心电
                case "ECG":
                    result = ccgProductEquipmentFeign.sentProductEnquipmentData(dto);
                    break;
                //病理
                case "PATH":
                    result = pathProductEquipmentFeign.updateDevices(dto);
                    break;
                //血库
                case "BBK":
                    result = bbkTwoProductEquipmentFeign.modifyInstInfoBy(dto);
                    break;
                //血透
                case "HD":
                    result = hdJfProductEquipmentFeign.modifyInstInfoBy(dto);
                    break;
                //手麻
                case "AIMS":
                    result = applyEquipmentInfoApi.updateMonitorBatch(dto);
                    break;
                //重症
                case "ICU":
                    result = icuisEquipControllerApi.updateEquipImsp(dto);
                    break;
                default:
                    break;
            }
            if (result.isSuccess()) {
                log.info("修改设备结果，============= , {}", result);
                //转换为 实体接收
                Map<String, Object> data = (Map<String, Object>) result.getData();
                imspUpdateProductResultVo.setIsUpdateStatus((Integer) data.get("isUpdateStatus"));
                imspUpdateProductResultVo.setProductCode((String) data.get("productCode"));
                return ResponseResult.success(imspUpdateProductResultVo);
            } else {
                return ResponseResult.error(FALLBACK, "交付修改产品设备失败," + result.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.error(FALLBACK, "交付修改产品设备失败," + e.getMessage());
        }
    }

    @Override
    public ResponseResult<HisLoginVO> hisLogin(AppLoginDTO dto) {
        log.info("调用云健康登录入参:{}", dto);
        ResponseResult<HisLoginVO> hisLoginVOResponseResult = systemLoginFeign.appLogin(dto);
        log.info("调用云健康登录 接口返回数据:{}", hisLoginVOResponseResult);
        return hisLoginVOResponseResult;
    }

    /**
     * 各产品设备自动检测
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult csmAutoCheckEquip(EquipAutoCheckDTO dto) {
        log.info("云健康调用设备自动检测入参:{}", JSONUtil.toJsonStr(dto));
        csmDataToApi.csmAutoCheckEquip(dto, getAuthorization());
        return ResponseResult.success();
    }

    private String getAuthorization() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串
        String old = "appid=" + appId + "&appsecret=" + publicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, publicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + appId + ";sign=" + sign;
        return headers;
    }

    @Override
    public ResponseResult changeHospital(ChangeHospitalDto dto) {
        log.info("调用云健康切换医院入参:{},", dto);
        ResponseResult<Object> hisLoginVOResponseResult = systemLoginFeign.changeHospital(dto);
        log.info("调用云健康登录 接口返回数据:{}", hisLoginVOResponseResult.getData());
        return hisLoginVOResponseResult;
    }

    @Override
    public ResponseResult getAllApp(GetAllAppDto dto) {
        log.info("调用云健康获取所有产品:{},", dto);
        ResponseResult<Object> hisLoginVOResponseResult = systemGetAllAppFeign.getAllApp(dto.getHospitaId(), dto.getUserId());
        log.info("调用云健康获取所有产品  接口返回数据:{}", hisLoginVOResponseResult.getData());
        return hisLoginVOResponseResult;
    }

    /**
     * 批量获取医院设备信息
     * <p>
     * 会根据ProductEquipmentDto中医院信息获取云健康医院的设备信息.
     * 可获取单体、区域、分院等单个或多个医院信息, 返回值会标注医院设备是否获取成功
     * </p>
     *
     * @param dto 请求医院信息
     * @return 返回汇总结果
     */
    public ResponseResult<Map<Long, EquipmentCompareWrapperResult>> getEquipmentDataBatch(SystemConfigDto<ProductEquipmentDto> dto) {
        log.warn("dto: {}", dto);
        if (ObjectUtil.isEmpty(dto)) {
            ResponseResult<Object> result = ResponseResult.error(FALLBACK);
            result.setMessage(result.getMessage() + ", 请求参数不能空.");
            return ResponseResult.error(FALLBACK);
        }
        if (CollUtil.isEmpty(dto.getData())) {
            ResponseResult<Object> result = ResponseResult.error(FALLBACK);
            result.setMessage(result.getMessage() + ", 产品信息不能空. ");
            return ResponseResult.error(FALLBACK);
        }
        // 分别获取医院产品设备信息
        List<ProductEquipmentDto> productEquipmentDtos = dto.getData();
        // 请求的医院数量
        log.info("获取云健康设备, 请求的医院数量, count: {}", dto.getData().size());
        log.info("获取云健康设备, 请求的详情, {}", dto.getData());
        // 单次获取每个医院的设备信息
        Map<Long, EquipmentCompareWrapperResult> resultMapData = MapUtil.newHashMap();
        for (ProductEquipmentDto productEquipmentDto : productEquipmentDtos) {
            EquipmentCompareWrapperResult wrapperResult = EquipmentCompareWrapperResult.builder().build();
            try {
                ResponseResult<List<OldNewEquipmentCompareDto>> result = getEquipmentData(productEquipmentDto.getHospitalId(),
                        productEquipmentDto.getHisOrgId(), productEquipmentDto.getProductCode());
                wrapperResult.setSuccess(false);
                log.info("批量获取云健康设备返回值. result: {}", result);
                if (ObjectUtil.isEmpty(result)) {
                    wrapperResult.setRemark("返回结果为空!");
                } else if (!result.isSuccess()) {
                    wrapperResult.setRemark(result.getMessage());
                } else {
                    wrapperResult.setEquipmentCompareDtos(result.getData());
                    wrapperResult.setSuccess(true);
                }
            } catch (Throwable e) {
                log.error("获取云健康设备异常. message: {}, e=", e.getMessage(), e);
                wrapperResult.setRemark("获取云健康设备异常. message: " + e.getMessage());
            }
            resultMapData.put(productEquipmentDto.getHospitalId(), wrapperResult);
        }
        return ResponseResult.success(resultMapData);
    }

    @Override
    public ResponseResult<List<OldNewEquipmentCompareDto>> getEquipmentData(SystemConfigDto<ProductEquipmentDto> dto) {
        return getEquipmentData(dto.getHospitalId(), dto.getHisOrgId(), dto.getData().get(0).getProductCode());
    }

    public ResponseResult<List<OldNewEquipmentCompareDto>> getEquipmentData(Long hospitalId, Long hisOrgId, String productCode) {
        ResponseResult<List<OldNewEquipmentCompareDto>> equipments = null;
        log.info("获取云健康设备列表获取的参数为: hospitalId:{}, hisOrgId: {}, productCode: {}", hospitalId, hisOrgId, productCode);
        try {
            VerifyEquipmentStatusDto verifyEquipmentStatusDto = new VerifyEquipmentStatusDto();
            verifyEquipmentStatusDto.setHospitalId(hospitalId);
            verifyEquipmentStatusDto.setHisOrgId(hisOrgId);
            log.info("调用{} 获取设备列表入参: {}", productCode, verifyEquipmentStatusDto);
            switch (productCode) {
                case "PACS":
                    equipments = pacsEquipmentFeign.getEquipments(verifyEquipmentStatusDto);
                    break;
                case "LIS":
                    equipments = lisBaseDataApi.getEquipmentInfo(verifyEquipmentStatusDto);
                    break;
                case "AIMS":
                    equipments = applyEquipmentInfoApi.retrieveMonitorsJf(verifyEquipmentStatusDto);
                    break;
//                //心电
//                case "ECG":
//                    equipments = lisBaseDataApi.getEquipmentInfoEcg(verifyEquipmentStatusDto);
//                    break;
//
//                //病理
//                case "PATH":
//                    equipments = lisBaseDataApi.getEquipmentInfoPath(verifyEquipmentStatusDto);
//                    break;
//
//                //血库
//                case "BBK":
//                    equipments = lisBaseDataApi.getEquipmentInfoBbk(verifyEquipmentStatusDto);
//                    break;
//
                //血透
                case "HD":
                    equipments = hdJfProductEquipmentFeign.selectAllMachine(verifyEquipmentStatusDto);
                    break;
                //重症
                case "ICU":
                    equipments = icuisEquipControllerApi.getEquipListByOrgIdImsp(verifyEquipmentStatusDto);
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            e.printStackTrace();
            equipments = new ResponseResult();
            equipments.setSuccess(false);
            equipments.setCode("500");
            equipments.setMessage("调用云健康设备接口出现错误...." + e.getMessage());

        }
        log.info("调用云健康获取产品{}设备列表返回值: {}", productCode, equipments);
        return equipments;

    }

    /**
     * 批量获取云健康设备检测状态
     * <p>
     * 根据医院获取
     * </p>
     *
     * @param dto 请求获取的医院设备信息
     * @return 返回医院设备检测结果详情
     */
    public ResponseResult<Map<Long, EquipmentStatusWrapperResult>> getEquipmentStatusBatch(SystemConfigDto<ProductEquipmentWrapperDto> dto) {
        log.warn("getEquipmentStatusBatch.dto: {}", dto);
        if (ObjectUtil.isEmpty(dto)) {
            ResponseResult<Object> result = ResponseResult.error(FALLBACK);
            result.setMessage(result.getMessage() + ", 请求参数不能空.");
            return ResponseResult.error(FALLBACK);
        }
        if (CollUtil.isEmpty(dto.getData())) {
            ResponseResult<Object> result = ResponseResult.error(FALLBACK);
            result.setMessage(result.getMessage() + ", 产品信息不能空. ");
            return ResponseResult.error(FALLBACK);
        }
        // 分别获取医院产品设备信息
        List<ProductEquipmentWrapperDto> productEquipmentDtos = dto.getData();
        // 请求的医院数量
        log.info("获取云健康设备, 请求的医院数量, count: {}", dto.getData().size());
        log.info("获取云健康设备, 请求的详情, {}", dto.getData());
        // 单次获取每个医院的设备信息
        Map<Long, EquipmentStatusWrapperResult> resultMapData = MapUtil.newHashMap();
        for (ProductEquipmentWrapperDto productEquipmentDto : productEquipmentDtos) {
            EquipmentStatusWrapperResult wrapperResult = EquipmentStatusWrapperResult.builder().build();
            try {
                ResponseResult<List<EquipmentStatusDto>> result = getEquipmentStatus(productEquipmentDto.getHospitalId(),
                        productEquipmentDto.getHisOrgId(), productEquipmentDto.getProductEquipmentDtoList());
                wrapperResult.setSuccess(false);
                log.info("获取云健康设备检测状态, 返回值. result: {}", result);
                if (ObjectUtil.isEmpty(result)) {
                    wrapperResult.setRemark("返回结果为空!");
                } else if (!result.isSuccess()) {
                    log.info("获取云健康设备检测状态失败. message: {}", result.getMessage());
                    wrapperResult.setEquipmentStatusDtos(result.getData());
                    wrapperResult.setRemark(result.getMessage());
                } else {
                    wrapperResult.setEquipmentStatusDtos(result.getData());
                    wrapperResult.setSuccess(true);
                }
            } catch (Throwable e) {
                log.error("获取云健康设备检测状态异常. message: {}, e=", e.getMessage(), e);
                wrapperResult.setRemark("获取云健康设备检测状态异常. message: " + e.getMessage());
            }
            resultMapData.put(productEquipmentDto.getHospitalId(), wrapperResult);
        }
        return ResponseResult.success(resultMapData);
    }

    public ResponseResult<List<EquipmentStatusDto>> getEquipmentStatus(SystemConfigDto<ProductEquipmentDto> dto) {
        return getEquipmentStatus(dto.getHospitalId(), dto.getHisOrgId(), dto.getData());
    }


    public ResponseResult<List<EquipmentStatusDto>> getEquipmentStatus(Long hospitalId, Long hisOrgId, List<ProductEquipmentDto> list) {
        //返回信息
        ResponseResult<List<EquipmentStatusDto>> resultList = new ResponseResult();
        List<EquipmentStatusDto> resultData = new ArrayList<>();
        //存在对照关系的 使用云健康id替换交付平台id
        for (ProductEquipmentDto d : list) {
            if (d.getOldEquipId() != null) {
                d.setId(d.getOldEquipId());
            }
        }
        Map<String, List<Long>> collect = list.stream().collect(Collectors.groupingBy(ProductEquipmentDto::getProductCode, Collectors.mapping(ProductEquipmentDto::getId, Collectors.toList())));
        Set<String> keys = collect.keySet();
        VerifyEquipmentStatusDto vDto = new VerifyEquipmentStatusDto();
        vDto.setHisOrgId(hisOrgId);
        vDto.setHospitalId(hospitalId);
        ResponseResult<List<EquipmentStatusDto>> devicesStatus = null;
        for (String key : keys) {
            List<Long> ids = collect.get(key);
            vDto.setIds(ids);
            log.info(key + "设备调用传参: {}", vDto);
            switch (key) {
                //PACS
                case "PACS":
                    devicesStatus = pacsEquipmentFeign.getDevicesStatus(vDto);
                    break;
                //心电
                case "ECG":
                    devicesStatus = ccgProductEquipmentFeign.getDevicesStatus(vDto);
                    break;
                //病理
                case "PATH":
                    devicesStatus = pathProductEquipmentFeign.getDevicesStatus(vDto);
                    break;
                //血库
                case "BBK":
                    break;
                //血透
                case "HD":
                    devicesStatus = hdJfProductEquipmentFeign.verifyMachineState(vDto);
                    break;
                //手麻
                case "AIMS":
                    devicesStatus = applyEquipmentInfoApi.verfyMonitorState(vDto);
                    break;
                //重症
                case "ICU":
                    devicesStatus = icuisEquipControllerApi.getDevicesStatus(vDto);
                    break;
                default:
                    //调用LIS
                    devicesStatus = lisBaseDataApi.equipmentStateCollate(vDto);
                    break;
            }
            log.info(key + "产品返回的结果: {}", devicesStatus);
            //返回值成功
            if ("0000".equals(devicesStatus.getCode())) {
                // 处理返回数据的类型转换，避免LinkedHashMap转换异常
                Object data = devicesStatus.getData();
                if (data instanceof List) {
                    log.info("{}产品返回的结果，data是List类型", key);
                    List<?> dataList = (List<?>) data;
                    for (Object item : dataList) {
                        if (item instanceof EquipmentStatusDto) {
                            log.info("{}产品返回的结果，List中元素是EquipmentStatusDto类型，直接添加", key);
                            // 如果已经是EquipmentStatusDto类型，直接添加
                            resultData.add((EquipmentStatusDto) item);
                        } else {
                            log.info("{}产品返回的结果，List中元素不是EquipmentStatusDto类型，通过JSON转化后添加", key);
                            try {
                                EquipmentStatusDto equipmentStatusDto = JSON.parseObject(JSON.toJSONString(item), EquipmentStatusDto.class);
                                log.info("{}产品返回的结果，List中元素不是EquipmentStatusDto类型，转化成功后添加", key);
                                resultData.add(equipmentStatusDto);
                            } catch (Exception e) {
                                log.error("{}产品返回的结果，List中元素不是EquipmentStatusDto类型，原始结果={}，转换发生异常，errMsg={}，stackInfo=", key, item, e.getMessage(), e);
                            }
                        }
                    }
                } else {
                    log.info("{}产品返回的结果，data不是List类型，实际类型={}", key, "null");
                }
            }
        }
        try {
            if (!CollectionUtils.isEmpty(resultData)) {
                resultData.forEach(item -> {
                    // 添加类型检查，确保类型安全
                    if (item instanceof EquipmentStatusDto) {
                        EquipmentStatusDto dto = (EquipmentStatusDto) item;  // 安全转换
                        if (dto.getCode() == TWO && StringUtils.isBlank(dto.getMsg()) && !StringUtils.isBlank(dto.getErrorMeg())) {
                            dto.setMsg(dto.getErrorMeg());
                        }
                    } else {
                        log.warn("resultData中发现非EquipmentStatusDto类型的元素: {}, 跳过处理", item != null ? item.getClass().getName() : "null");
                    }
                });
            }
        } catch (Exception e) {
            log.error("转换处理接口返回结果，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        resultList.setData(resultData);
        return resultList;
    }


    @Override
    public ResponseResult<SystemSettingResultDto> batchCancelSystemAuthorized(SystemConfigDto<SystemAuthorizeInfoSaveDto> dto) {
        ResponseResult responseResult = null;
        // 取消授权
        List<SystemAuthorizeInfoSaveDto> data = dto.getData();
        for (SystemAuthorizeInfoSaveDto item : data) {
            item.setHisOrgId(dto.getHisOrgId());
            item.setOrgId(dto.getHisOrgId());
            item.setHospitalId(dto.getHospitalId());
        }
        log.info("调用基础中台发送的数据..." + data);
        responseResult = productEncryptionFeign.batchCancelSystemAuthorized(data);
        log.info("基础中台返回的数据: " + responseResult.toString());
        return responseResult;
    }

    @Override
    public ResponseResult clearMessageData(BatchInsertGeneralDTO<SendMessageValueDto> dto) {
        log.error("入参{}", dto);
        return systemMessageFeign.clearMessage(dto.getData());
    }

    /**
     * 重置默认密码
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<String> resetDefaultPassword(ProductEncryptDto dto) {
        log.error("重置密码入参：{}", dto);
        LoginUserInfo loginUserInfo = LoginUserContext.getLoginUserInfo();
        if (loginUserInfo != null) {
            loginUserInfo.setHospitalId(dto.getHospitalId());
            loginUserInfo.setOrgId(dto.getHisOrgId());
            LoginUserContext.mock(loginUserInfo);
        }
        ResponseResult<String> result = systemAccountFeign.resetDefaultPassword(dto.getProductCode());
        log.error("重置密码返回：{}", result);
        return result;
    }

    /**
     * 保存初始化信息1
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult saveInitializationData(DataTransDto dto) {
        log.error("保存初始化信息1入参：{}", dto);
        ResponseResult<Object> result = dataTransApplicationFeign.saveInitializationData(dto);
        log.error("保存初始化信息1返回结果：{}", result);
        return result;
    }

    /**
     * 保存初始化信息2
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult saveDataTransLog(DataTransDto dto) {
        log.error("保存初始化信息2入参：{}", dto);
        ResponseResult<Object> result = dataTransApplicationFeign.saveDataTransLog(dto);
        log.error("保存初始化信息2返回结果：{}", result);
        return result;
    }

    /**
     * 查询产品授权结果数据
     *
     * @param req
     * @return
     */
    @DS("commRead")
    @Override
    public ResponseResult queryProductAuthResult(ProductAuthReqDTO req) {
        log.info("查询产品授权结果数据");
        List<ProductAuthResultDTO> list = systemSettingScriptMapper.queryProductAuthResult();
        log.info("查询产品授权结果数据结果数量：{}", list.size());
        return ResponseResult.success(list);
    }

    /**
     * 获取Lis推荐检验项目
     *
     * @param dto
     * @return
     */
    public ResponseResult<List<LisEquipmentItemDTO>> queryLabItemByEquipment(AvailableLabQueryDTO dto) {
        return lisBaseDataApi.queryLabItemByEquipment(dto);
    }

}
