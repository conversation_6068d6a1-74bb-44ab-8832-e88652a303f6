package com.msun.core.component.implementation.server.msuncloud;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.GuijieTest;
import com.msun.core.springcloud.server.annotation.server.MsunRestController;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/27 19:55
 */
@Slf4j
@MsunRestController
@Api(tags = "云健康接口调用")
public class GuijieTestImpl implements GuijieTest {
    /**
     * 代办任务检测测试
     *
     * @param args
     * @return
     */
    @Override
    public ResponseResult<JSONObject> todoTaskTask(TodoTaskCheckArgs args) {
        JSONObject obj = new JSONObject();
        obj.put("hospitalId", args.getHospitalId());
        obj.put("orgId", args.getOrgId());
        obj.put("taskCode", args.getTaskCode());
        obj.put("flag", true);
        obj.put("errCount", 0);
        obj.put("reason", "测试成功");
        obj.put("detailList", Collections.emptyList());
        return ResponseResult.success(obj);
    }
}
