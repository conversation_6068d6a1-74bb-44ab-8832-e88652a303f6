package com.msun.core.component.implementation.server.msuncloud;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.msuncloud.MsunCloudApi;
import com.msun.core.component.implementation.server.utils.NacosUtil;
import com.msun.core.springcloud.server.annotation.server.MsunRestController;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/18 13:46
 */
@Slf4j
@MsunRestController
@Api(tags = "云健康接口调用")
public class MsunCloudApiImpl implements MsunCloudApi {
    /**
     * 调用接口
     *
     * @param args
     * @return
     */
    @Override
    public ResponseResult<?> callApi(CallArgs args) {
        List<NacosUtil.NacosInstanceDTO> instances = NacosUtil.getInstanceByServiceName(args.getServiceName(), args.getNamespaceId(), args.getClusterName(), args.getGroupName(), true);
        if (CollUtil.isEmpty(instances)) {
            String errMsg = StrUtil.format("traceId({}) - 用户【{}#{}】从Nacos中获取服务【{}】失败;namespaceId: {}, clusterName: {}, groupName: {}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), args.getServiceName(), args.getNamespaceId(), args.getClusterName(), args.getGroupName());
            log.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        NacosUtil.NacosInstanceDTO service = CollUtil.getFirst(instances);
        UrlBuilder urlBuilder = new UrlBuilder();
        urlBuilder.setHost(service.getIp());
        urlBuilder.setPort(service.getPort());
        urlBuilder.addPath(args.getApiUrl());
        if (CollUtil.isNotEmpty(args.getApiQueryArgs())) {
            for (Map.Entry<String, Object> entry : args.getApiQueryArgs().entrySet()) {
                urlBuilder.addQuery(entry.getKey(), entry.getValue());
            }
        }
        String url = urlBuilder.build();
        HttpRequest request = HttpUtil.createRequest(Method.valueOf(args.getApiMethod()), url);
        if (args.getApiHeaders() == null) {
            args.setApiHeaders(new HashMap<>(2));
        }
        args.getApiHeaders().put("traceId", args.getTraceId());
        args.getApiHeaders().put("domain", Base64.encode(args.getDomain()));
        request.headerMap(args.getApiHeaders(), true);
        if (StrUtil.isNotBlank(args.getApiBodyArgs())) {
            request.body(args.getApiBodyArgs());
        }
        HttpResponse execute;
        try {
            execute = request.execute();
            if (Boolean.FALSE.equals(execute.isOk())) {
                String errMsg = StrUtil.format("traceId({}) - 用户【{}#{}】调用云健康服务【{}】接口【{} - {}】失败；\nheaders: {}\nbody: {}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), args.getServiceName(), args.getApiMethod(), url, args.getApiHeaders(), args.getApiBodyArgs());
                log.error(errMsg);
                throw new RuntimeException(errMsg);
            }
        } catch (Throwable e) {
            String errMsg = StrUtil.format("traceId({}) - 用户【{}#{}】调用云健康服务【{}】接口【{} - {}】失败: {}；\nheaders: {}\nbody: {}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), args.getServiceName(), args.getApiMethod(), url, e.getMessage(), args.getApiHeaders(), args.getApiBodyArgs());
            log.error(errMsg, e);
            throw new RuntimeException(errMsg, e);
        }
        String body = execute.body();
        log.info("traceId({}) - 用户【{}#{}】调用云健康服务【{}】接口【{} - {}】成功", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), args.getServiceName(), args.getApiMethod(), url);
        return JSON.parseObject(body, ResponseResult.class);
    }
}
