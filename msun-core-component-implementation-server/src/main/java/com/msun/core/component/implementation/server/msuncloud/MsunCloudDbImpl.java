package com.msun.core.component.implementation.server.msuncloud;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;

import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.component.implementation.api.msuncloud.MsunCloudDb;
import com.msun.core.component.implementation.server.utils.PageHelperUtil;
import com.msun.core.db.exceptions.MsunDbException;
import com.msun.core.springcloud.server.annotation.server.MsunRestController;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/27 14:07
 */
@Slf4j
@MsunRestController
@Api(tags = "执行云健康数据库脚本")
public class MsunCloudDbImpl implements MsunCloudDb {

    private static final String CHIS = "chis";
    private static final String CHISAPP = "chisapp";
    private static final String CDRAPP = "cdrapp";
    private static final String DATAWAREHOUSE = "datawarehouse";

    @Autowired
    private MsunCloudMapper msunCloudMapper;

    @Lazy
    @Autowired
    private MsunCloudDbImpl self;

    /**
     * 执行查询脚本
     *
     * @param args
     * @return
     */
    @Override
    public ResponseResult<List<JSONObject>> execQuery(ExecSqlArgs args) {
        String sql = null;
        int maxSize = 9999;
        try {
            try {
                sql = AesUtil.decrypt(args.getAesEnSql());
            } catch (Throwable e) {
                throw new MsunDbException("解密脚本失败");
            }
            List<JSONObject> list = new ArrayList<>();

            if (Boolean.TRUE.equals(args.getByStreamQuery())) {
                //使用self.streamQuery，避免事务失效
                list.addAll(self.streamQuery(args.getDbName(), sql));
            } else {
                int pageNum = 1;
                while (true) {
                    List<JSONObject> subList = pageQuery(args.getDbName(), sql, pageNum, maxSize);
                    if (CollUtil.isEmpty(subList)) {
                        break;
                    }
                    list.addAll(subList);
                    pageNum++;
                }
            }
            return ResponseResult.success(list);
        } catch (Throwable e) {
            log.error(StrUtil.format("traceId({}) - 用户【{}#{}】执行查询脚本失败，原因：{}\n脚本：{}|{}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), e.getMessage(), args.getDbName(), sql), e);
            throw new RuntimeException(StrUtil.format("traceId({}) - 用户【{}#{}】执行查询脚本失败，原因：{}\n脚本：{}|{}", args.getTraceId(), args.getYyUserAccount(), args.getYyUserName(), e.getMessage(), args.getDbName(), sql), e);
        }
    }

    /**
     * 流式查询(pgsql必须开启事务，并设置fetchSize后才能生效)
     *
     * @param dbName
     * @param sql
     * @return
     * @throws IOException
     */
    public List<JSONObject> streamQuery(String dbName, String sql) throws IOException {
        List<JSONObject> list = new ArrayList<>();
        log.info("streamQuery，dbName={}，当前数据源: {}", dbName, DynamicDataSourceContextHolder.peek());
        if (StrUtil.equalsIgnoreCase(dbName, CHIS)) {
            try (Cursor<JSONObject> cursor = msunCloudMapper.chisStreamQuery(sql)) {
                cursor.forEach(list::add);
            }
        } else if (StrUtil.equalsIgnoreCase(dbName, CHISAPP)) {
            try (Cursor<JSONObject> cursor = msunCloudMapper.chisappStreamQuery(sql)) {
                cursor.forEach(list::add);
            }
        } else if (StrUtil.equalsIgnoreCase(dbName, DATAWAREHOUSE)) {
            try (Cursor<JSONObject> cursor = msunCloudMapper.datawarehouseStreamQuery(sql)) {
                cursor.forEach(list::add);
            }
        } else if (StrUtil.equalsIgnoreCase(dbName, CDRAPP)) {
            try (Cursor<JSONObject> cursor = msunCloudMapper.cdrappStreamQuery(sql)) {
                cursor.forEach(list::add);
            }
        } else {
            throw new MsunDbException("不支持的数据库名称");
        }
        return list;
    }

    private List<JSONObject> pageQuery(String dbName, String sql, int pageNum, int pageSize) {
        sql = StrUtil.format("SELECT * FROM ( \n {} \n ) as tbl_tmp", String.valueOf(sql).trim());
        String finalSql = sql;
        return PageHelperUtil.queryPage(pageNum, pageSize, page -> {
            if (StrUtil.equalsIgnoreCase(dbName, CHIS)) {
                return msunCloudMapper.chisQuery(finalSql);
            } else if (StrUtil.equalsIgnoreCase(dbName, CHISAPP)) {
                return msunCloudMapper.chisappQuery(finalSql);
            } else if (StrUtil.equalsIgnoreCase(dbName, DATAWAREHOUSE)) {
                return msunCloudMapper.datawarehouseQuery(finalSql);
            } else if (StrUtil.equalsIgnoreCase(dbName, CDRAPP)) {
                return msunCloudMapper.cdrappQuery(finalSql);
            } else {
                throw new MsunDbException("不支持的数据库名称");
            }
        });
    }
}
