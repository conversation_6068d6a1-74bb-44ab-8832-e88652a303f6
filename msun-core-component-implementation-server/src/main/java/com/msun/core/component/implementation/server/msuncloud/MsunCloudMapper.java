package com.msun.core.component.implementation.server.msuncloud;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cursor.Cursor;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/28 8:41
 */
@Mapper
public interface MsunCloudMapper {

    /**
     * 在chis库流式查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("chis")
    @Options(fetchSize = 100)
    Cursor<JSONObject> chisStreamQuery(@Param("querySql") String querySql);

    /**
     * 在chisapp库流式查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("chisapp")
    @Options(fetchSize = 100)
    Cursor<JSONObject> chisappStreamQuery(@Param("querySql") String querySql);

    /**
     * 在cdrapp库流式查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("cdrapp")
    @Options(fetchSize = 100)
    Cursor<JSONObject> cdrappStreamQuery(@Param("querySql") String querySql);

    /**
     * 在datawarehouse库流式查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("datawarehouse")
    @Options(fetchSize = 100)
    Cursor<JSONObject> datawarehouseStreamQuery(@Param("querySql") String querySql);


    /**
     * 在chis库全量查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("chis")
    List<JSONObject> chisQuery(@Param("querySql") String querySql);

    /**
     * 在chisapp库全量查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("chisapp")
    @Options(fetchSize = 100)
    List<JSONObject> chisappQuery(@Param("querySql") String querySql);

    /**
     * 在cdrapp库全量查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("cdrapp")
    @Options(fetchSize = 100)
    List<JSONObject> cdrappQuery(@Param("querySql") String querySql);

    /**
     * 在datawarehouse库全量查询
     * @param querySql
     * @return
     */
    @Select("${querySql}")
    @DS("datawarehouse")
    @Options(fetchSize = 100)
    List<JSONObject> datawarehouseQuery(@Param("querySql") String querySql);
}
