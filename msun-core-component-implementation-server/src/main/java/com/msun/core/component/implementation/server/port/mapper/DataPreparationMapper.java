package com.msun.core.component.implementation.server.port.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2023/11/17/10:49
 */
@Mapper
public interface DataPreparationMapper {


    /**
     * 在chis库执行自定义SQL
     *
     * @param checkSql 自定义SQL
     * @return SQL执行结果
     */
    @Select("${checkSql}")
    @DS("chis")
    List<JSONObject> executeChisDb(@Param("checkSql") String checkSql);

    /**
     * 在chisapp库执行自定义SQL
     *
     * @param checkSql 自定义SQL
     * @return SQL执行结果
     */
    @Select("${checkSql}")
    @DS("chisapp")
    List<JSONObject> executeChisappDb(@Param("checkSql") String checkSql);

    /**
     * 在chisapp库执行自定义SQL
     *
     * @param checkSql 自定义SQL
     * @return SQL执行结果
     */
    @Select("${checkSql}")
    @DS("datawarehouse")
    List<JSONObject> executeDataWareHouseDb(@Param("checkSql") String checkSql);

    /**
     * 在cdrapp库执行自定义SQL
     *
     * @param checkSql 自定义SQL
     * @return SQL执行结果
     */
    @Select("${checkSql}")
    @DS("cdrapp")
    List<JSONObject> executeCdrappDb(@Param("checkSql") String checkSql);

}
