server.port=10019
spring.application.name=msun-core-component-implementation-server
spring.main.allow-bean-definition-overriding=true
server.servlet.context-path=/
swagger.enable=true
swagger.group-list.group0.title=\u5FEB\u901F\u5B9E\u65BD\u5E73\u53F0
swagger.group-list.group0.description=\u5FEB\u901F\u5B9E\u65BD\u5E73\u53F0
swagger.group-list.group0.version=1.0.0
swagger.group-list.group0.scanPackage=com.msun.core.component.implementation.server

mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.mapper-locations=classpath*:mybatis/**/*.xml
mybatis-plus.type-aliases-package=com.msun.core.component.implementation.server.*.entity.po
msun.cors=false
logging.config=classpath:config/logback.xml
#######################################################################
spring.cloud.nacos.server-addr=nacos.chis.msunsoft.com:8848
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.namespace=default
spring.cloud.nacos.config.enabled=true
spring.cloud.nacos.config.refresh-enabled=true
spring.cloud.nacos.config.namespace=default

msun.ds.datasource.master=<EMAIL>
msun.ds.datasource.commRead=HIS_MASTER@chis.comm_read
msun.ds.datasource.chisapp=<EMAIL>
msun.ds.datasource.datawarehouse=<EMAIL>
msun.ds.datasource.dataapplication=HIS_MASTER@chisapp.dataapplication_app
msun.ds.datasource.msunreportcloud=HIS_MASTER@chisapp.msunreportcloud_app
msun.ds.datasource.slave=HIS_SLAVE@chis.peis_app

spring.cloud.nacos.config.shared-configs[0].data-id=config-database-implementation.properties
#spring.cloud.nacos.config.shared-configs[0].data-id=config-database-comm.properties
spring.cloud.nacos.config.shared-configs[0].refresh=true
spring.cloud.nacos.config.shared-configs[1].data-id=config-common-endpoint.properties
spring.cloud.nacos.config.shared-configs[1].refresh=true
spring.cloud.nacos.config.shared-configs[2].data-id=config-common-page-helper.properties
spring.cloud.nacos.config.shared-configs[2].refresh=true
spring.cloud.nacos.config.shared-configs[3].data-id=config-common-feign.properties
spring.cloud.nacos.config.shared-configs[3].refresh=true
spring.cloud.nacos.config.shared-configs[4].data-id=config-common-seata.properties
spring.cloud.nacos.config.shared-configs[4].refresh=true
#redis??
spring.cloud.nacos.config.shared-configs[5].data-id=config-common-redis.properties
spring.cloud.nacos.config.shared-configs[5].refresh=true
# ???? ?
spring.cloud.nacos.config.shared-configs[6].data-id=config-common-domain.properties
spring.cloud.nacos.config.shared-configs[6].refresh=true
#rocketmq\u914D\u7F6E
spring.cloud.nacos.config.shared-configs[7].data-id=config-common-rocket-mq.properties
spring.cloud.nacos.config.shared-configs[7].refresh=true


eureka.client.enabled=false
ribbon.eureka.enabled=false
spring.application.base.dict.name=msun-middle-base-dict
spring.application.common.name=msun-middle-base-common
spring.application.resource.name = msun-middle-base-resource
spring.application.pacs.name = msun-middle-business-pacs
spring.application.peis.name = msun-peis-app-peis-new
spring.application.pis.name = msun-pathology-app-pis
spring.application.bis.name = msun-blood-app-bis
spring.application.businessnursedoc.name=msun-middle-business-nursedoc
#tag--version -- release#
tag.version=release-V1.74.0


#???? IP ????????
restrict.ip.address=************

#\u5355\u4E2A\u6700\u5927\u6587\u4EF6\u5927\u5C0F
spring.servlet.multipart.max-file-size=20MB
#\u8BF7\u6C42\u4E2D\u4E0A\u4F20\u603B\u6587\u4EF6\u5927\u5C0F
spring.servlet.multipart.max-request-size=20MB

# csm ?????????appid
csm.appId=391f4d04011c440eb0bde1e29f231678
csm.publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzJJLFWoTKqkV8XbEBOI4S3XJ+h0P3yQVWuzr6W7aoTuvb/a7Aqd/a1PH6tTtp/bDM92ZYk5Hzw3X0j2bzl4zvYZxuUCgQ9hXizRex7NxK/8G8Kx/nsCd1JM37jWn6G/caPsM68eEzD7sfCPSgANcJZkp17Pjey0st2+4GQf7TYpboQsw8z+WOXEX5bka5cUoLrkgGDNToi6gm3LDxH45mu4VxL0Zm+WjGZ2scTheqji0nv6THXiXY3h6UfjfdPUXE0nPe4yAGZv/yjutcOUjIZoBSIIkw6lKo2oPx0JJTB0omYAUMwPRfzliCAa3V9iy1Cvyv6HfEsSm3V5WWiBWRwIDAQAB