<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.AccountMapper">
    <sql id="baseSql">
        account_Id
        as accountId,
        account_Name as accountName,
        password as password,
        hospital_Id as hospitalId,
        hospital_Name as hospitalName,
        dept_Id as deptId,
        dept_Name as deptName,
        user_Id as userId,
        user_Code as userCode,
        user_Name as userName,
        invalid_Flag as invalidFlag,
        login_Time as loginTime,
        login_Failed_Count as loginFailedCount,
        version as version
    </sql>
    <select id="findRepeatAccountCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        account
        <where>
            <if test="accountId != null">
                and account_Id != #{accountId}
            </if>
            and account_Name = #{accountName}
            and hospital_id = #{hospitalId}
        </where>
    </select>
    <select id="findRepeatAccountCountByUser" resultType="java.lang.Integer">
        select
        count(*)
        from
        account
        <where>
            <if test="accountId != null">
                and account_Id != #{accountId}
            </if>
            and account_Name = #{accountName}
            and user_Id = #{userId}
        </where>
    </select>
    <select id="getAllAccount" resultType="account">
        SELECT
        <include refid="baseSql"></include>
        FROM comm.account
        WHERE account_name != 'admin' AND invalid_flag = '0' AND hospital_id = #{hospitalId}
    </select>
    <update id="updateAllAccount">

        <foreach item="roles" collection="roleList" open="(" separator="," close=")">
            #{roles}
        </foreach>
    </update>

</mapper>