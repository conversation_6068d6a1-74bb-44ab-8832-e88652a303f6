<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.aims.mapper.AimsConfigItemMapper">

    <select id="selectConfigItemByParams" resultType="com.msun.core.component.implementation.api.aims.dto.ConfigItemDTO">
        select * from oas.config_item where hospital_id =-1
    </select>

    <delete id="deleteConfigItemByParams" parameterType="com.msun.core.component.implementation.api.aims.dto.HospitalAndOrgDTO">
        delete from oas.config_item where hospital_id = #{hospitalId}
        <if test="isVersion">
            and config_code in ('version','autographVersion','edition')
        </if>
        <if test="!isVersion">
            and config_code != 'version'
        </if>
    </delete>
    <select id="getCapturedDataLog" parameterType="java.util.List" resultType="java.lang.Long">
        select monitor_id
        from oas.captured_data_log
        where monitor_id in
        <foreach collection="list" index="index" item="item"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        group by monitor_id
    </select>
</mapper>
