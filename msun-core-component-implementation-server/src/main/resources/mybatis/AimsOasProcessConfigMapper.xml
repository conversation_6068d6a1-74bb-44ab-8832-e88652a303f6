<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.aims.mapper.AimsOasProcessConfigMapper">

    <sql id="baseSql">
        process_config_id,
        process_name,
        sort,
        process_path,
        process_type,
        process_input_code,
        process_input_full_code,
        process_config_code,
        expend,
        menu_flag,
        invalid_flag,
        page_source,
        process_table,
        hospital_id,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        process_icon,
        process_parent_code,
        node_code,
        allow_modify_flag,
        form_max_no,
        desktop_style_type
    </sql>

    <select id="selectOasProcessConfigByParams" resultType="java.util.Map">
        select * from oas.process_config where hospital_id =-1
    </select>

    <delete id="deleteOasProcessConfigByParams" parameterType="com.msun.core.component.implementation.api.aims.dto.HospitalAndOrgDTO">
        delete from oas.process_config where hospital_id = #{hospitalId}
    </delete>

    <delete id="deleteCustomForms" parameterType="com.msun.core.component.implementation.api.aims.dto.HospitalAndOrgDTO">
        delete from oas.custom_form where hospital_id = #{hospitalId}
    </delete>

    <delete id="deleteCustomFormIdList" parameterType="java.util.List">
        UPDATE oas.custom_form SET invalid_flag='1' WHERE
        custom_form_id IN
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
                                                      AND invalid_flag='0'
    </delete>

    <select id="findOasProcessConfigByParams"
            resultType="com.msun.core.component.implementation.api.aims.vo.OasProcessConfigVO">
        select <include refid="baseSql"></include>
        from oas.process_config where hospital_id = #{hospitalId}
        and process_config_code in
            <foreach collection="processConfigCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>
