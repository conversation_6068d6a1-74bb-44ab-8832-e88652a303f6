<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.msunportal.dao.BasicResearchGkMapper">
    <!-- 发票大类 - 修改发票类型 -->
    <update id="updateItemType"
            parameterType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">
        update comm.dict_invoice_item
        set his_update_time   = now(),
            dict_item_type_id = ${dictItemTypeId}
        where invoice_item_id = ${invoiceItemId}
          and (hospital_id = #{hospitalId} or hospital_id = -1)
    </update>
    <update id="updateDeptcategory"
            parameterType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">
        update comm.config_in_dept
        set his_update_time = now(),
            open_bed_number = #{openBedNumber}
        where dept_id = #{deptId}
          and (hospital_id = #{hospitalId} or hospital_id = -1)
    </update>
    <!-- 启用指标单个口径 -->
    <update id="enableAper">
        update portal.portal_interface_index_info
        set
            his_update_time = now(),
            enabled = 1
        where index_code = #{indexCode} and aper_code = #{aperCode}
    </update>
    <!-- 禁用指标所有口径 -->
    <update id="disableAllAper">
        update portal.portal_interface_index_info
        set
            his_update_time = now(),
            enabled = 0
        where index_code = #{indexCode}
    </update>
    <select id="selectMaintainOpenBed"
            parameterType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo"
            resultType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">
        select cd.open_bed_number                                     as openBedNumber,
               d.dept_name                                            as deptName,
               d.dept_id                                            as deptCode,
               d.category_id                                          as categoryId,
               case when d.invalid_flag = '0' then '启用' else '禁用' end AS invalidFlag,
               case when cd.open_bed_number is not null and cd.open_bed_number != 0 then '已维护' else '未维护' end "dataSource"
        from comm.config_in_dept cd
                 left join
             comm.dept d on
                 cd.dept_id = d.dept_id
        where d.invalid_flag = '0'
          and d.category_id = '3'
          AND (cd.hospital_id = #{hospitalId} or cd.hospital_id = -1)
    </select>
    <select id="selectDeptcategory"
            parameterType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo"
            resultType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">

        select case
                   when d.category_id = '1' then '门诊科室'
                   WHEN d.category_id = '2' then '收款处'
                   WHEN d.category_id = '3' then '病房'
                   WHEN d.category_id = '4' then '药房'
                   WHEN d.category_id = '5' then '药库'
                   WHEN d.category_id = '6' then '医技科室'
                   WHEN d.category_id = '7' then '手术室'
                   WHEN d.category_id = '8' then '行政科室'
                   WHEN d.category_id = '9' then '护理单元'
                   WHEN d.category_id = '10' then '静配中心'
                   WHEN d.category_id = '11' then '乡医工作站'
                   WHEN d.category_id = '11' then '材料库'
                   END as categoryId
        from comm.dept d
    </select>
    <!-- 发票大类 - 查询项目列表 -->
    <select id="selectMaintainItem"
            resultType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">
        select invoice_item_id          "invoiceItemId",
               invoice_item_code        "invoiceItemCode",
               invoice_item_name        "invoiceItemName",
               item.dict_item_type_id   "dictItemTypeId",
               dict.dict_item_type_code "dictItemTypeCode",
               dict.dict_item_type_name "dictItemTypeName",
               case when item.dict_item_type_id is not null then '已维护' else '未维护' end "dataSource"
        from comm.dict_invoice_item item
                 left join comm.dict_item_type dict on item.dict_item_type_id = dict.dict_item_type_id
        where item.invalid_flag = '0'
          and item.hospital_id = #{hospitalId} or item.hospital_id = -1
        order by "invoiceItemName"
    </select>
    <!-- 发票大类 - 查询发票类型 -->
    <select id="selectItemTypeList"
            resultType="com.msun.core.component.implementation.server.msunportal.po.BasicResearchGkPo">
        select dict.dict_item_type_id   "dictItemTypeId",
               dict.dict_item_type_code "dictItemTypeCode",
               dict.dict_item_type_name "dictItemTypeName"
        from comm.dict_item_type dict
        where invalid_flag = '0'
          and dict.hospital_id = #{hospitalId} or dict.hospital_id = -1
        order by order_no
    </select>
    <!-- 查询指标列表 -->
    <select id="getIndexList" resultType="com.msun.core.component.implementation.server.msunportal.po.IndexPo">
        select distinct index_code "indexCode",
                        index_name "indexName",
                        aper_code  "aperCode",
                        aper_name  "aperName",
                        caliber_desc "caliberDesc",
                        order_no   "orderNo"
        from portal.portal_interface_index_info
        where enabled = 1
        <if test="indexName != null and indexName != ''">
            and index_name ~ #{indexName}
        </if>
        order by order_no
    </select>
    <!-- 查询指标口径列表 -->
    <select id="getIndexAperList"
            resultType="com.msun.core.component.implementation.server.msunportal.po.IndexPo">
        select aper_code "aperCode",
               aper_name "aperName",
               caliber_desc "caliberDesc",
               enabled
        from portal.portal_interface_index_info
        where index_code = #{indexCode}
        order by "aperCode"
    </select>
    <select id="selectDddMaintain" resultType="java.util.Map">
        select
            sum(case when nullif(t.ddd, '') :: numeric != 0 and nullif(t1.ddds_count, 0)!= 0 then 1 else 0 end) "DddNum",
            round((cast(sum(case when nullif(t.ddd, '')::numeric != 0 and nullif(t1.ddds_count, 0) != 0 then 1 else 0 end) as numeric)) / (cast(count(1) as numeric)) * 100,2) as "integrity",
            count(1) "allDddNum"
        from
            comm.dict_drug_basic_main t
                left join comm.dict_drug_basic_branch t1 on t.drug_basic_main_id = t1.drug_basic_main_id
        where
            t.antibiotics_level_id > 0
          and t.invalid_flag = '0'

    </select>
    <select id="selectDddMaintainDetails" resultType="java.util.Map">
        select
            drug_name as "drugName",
            t.ddd "ddd",
            t1.ddds_count "dddsCount"
        from
            comm.dict_drug_basic_main t
            left join comm.dict_drug_basic_branch t1 on t.drug_basic_main_id = t1.drug_basic_main_id

        where t.antibiotics_level_id > 0 and t.invalid_flag = '0'
        group by t.ddd,t.drug_name,t1.ddds_count
        HAVING count(1) - sum(case when nullif(t.ddd, '') :: numeric != 0 and nullif(t1.ddds_count, 0) != 0 then 1 else 0 end)  <![CDATA[ <> ]]> 0

    </select>
    <select id="selectInvoiceItemCheck" resultType="java.lang.Boolean">
        select
            case when count(1) = 0 then true else false end "checkValue"
        from comm.dict_invoice_item
        where dict_item_type_id is null and invalid_flag = '0'
        <if test="hospitalId != null and hospitalId != ''">
            and (hospital_id = #{hospitalId} or hospital_id = -1)
        </if>
    </select>
    <select id="selectOpenBedCheck" resultType="java.lang.Boolean">
        select
            case when count(1) = 0 then true else true end "checkValue"
        from comm.config_in_dept cd
            left join comm.dept d on cd.dept_id = d.dept_id
        where cd.invalid_flag = '0' and (cd.open_bed_number is null or cd.open_bed_number = 0) and d.category_id = '3'
        <if test="hospitalId != null and hospitalId != ''">
            and (cd.hospital_id = #{hospitalId} or cd.hospital_id = -1)
        </if>
    </select>

</mapper>
