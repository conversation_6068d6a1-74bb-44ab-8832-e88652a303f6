<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.bedchargeconfig.dao.BedChargeConfigMapper">
    <select id="getOcId" resultType="java.lang.Integer">
        select orde.order_category_id from  comm.dict_charge_price dcp
            left join comm.dict_order_vs_charge nvschar on dcp.charge_id = nvschar.charge_id
            left join comm.dict_nurse_order dno on nvschar.order_id = dno.dict_nurse_order_id
            left join comm. dict_order_doc_vs_nurse odvn on dno.dict_nurse_order_id = odvn.dict_nurse_order_id
            left join comm. dict_order orde on orde.dict_order_id = odvn.dict_order_id
            where dcp.charge_id = #{chargePriceId}
            limit 1
    </select>
</mapper>