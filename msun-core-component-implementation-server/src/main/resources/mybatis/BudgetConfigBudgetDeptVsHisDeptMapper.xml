<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.budget.dao.BudgetConfigBudgetDeptVsHisDeptMapper">

    <select id="getUserListByUserName"
            resultType="com.msun.core.component.implementation.server.budget.entity.dto.BudgetUserDto">
        select a.user_id as userId,
        a.user_code as userAccountId,
        a.user_name  as userName,
        a.dept_id as mainDeptId,
        b.dept_id as deptId,
        b.dept_name as deptName
        from dwd.dwd_user a
        left join dwd.dwd_identity b
        on a.user_id::numeric = b.user_id
        where 1=1
        <if test="deptId != null">
            and ( a.dept_id  = #{deptId} or b.dept_id = #{deptId})
        </if>
        <if test="userNameList != null and userNameList.size > 0">
            and a.user_name in
            <foreach collection="userNameList" item="userName" separator="," open="(" close=")">
                #{userName}
            </foreach>
        </if>
    </select>

</mapper>