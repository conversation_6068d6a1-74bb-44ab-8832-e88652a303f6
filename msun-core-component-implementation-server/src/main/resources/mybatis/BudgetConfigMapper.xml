<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.budget.dao.BudgetConfigMapper">

    <delete id="deleteBudgetDeptData" parameterType="java.lang.Long">
        delete from budget.bg_dept_dict where 1 = 1 and parent_dept_id != #{parentDeptId}
    </delete>

    <!--查询预算系统所有核算科室字典 -->
    <select id="getAllBudgetDept"
            resultType="com.msun.core.component.implementation.server.budget.entity.po.BudgetDeptDictPo">
        select dept_id as deptId,
               dept_name as deptName,
               hospital_id as hospitalId,
               parent_dept_id as parentDeptId
        from
            budget.bg_dept_dict
        where parent_dept_id !='0'
    </select>

    <!-- 查询预算系统所有预算科目-->
    <select id="getAllSubject"
            resultType="com.msun.core.component.implementation.server.budget.entity.po.BudgetTypePO">
        select
            expend_code as expendCode,
            expend_name as expendName,
            parent_expend_code as parentExpendCode
        from budget.bg_budget_type_dict
        where deleted ='0'
    </select>
    <select id="judgeIsExistDataInBgDeptVsExpendCode" resultType="java.lang.Integer">
        select 1
        from  budget.bg_dept_vs_expendcode
        where true
        limit 1
    </select>
    <select id="judgeIsExistDataInDeptDict" resultType="java.lang.Integer">
        select 1
        from  budget.bg_dept_dict
        where true
            limit 1
    </select>

    <insert id="insertBatchBudgetDept" parameterType="java.util.List">
        insert into budget.bg_dept_dict(dept_id,dept_name,parent_dept_id,flag_invalid,dept_alias,
                                          hospital_id,his_org_id,his_update_time) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
        (
        #{tmpData.deptId}, #{tmpData.deptName},#{tmpData.parentDeptId},#{tmpData.flagInvalid},#{tmpData.deptAlias},
            #{tmpData.hospitalId},#{tmpData.hisOrgId},now()
        )
        </foreach>
    </insert>


    <delete id="deleteMergedData" parameterType="java.lang.Long">
        delete from budget.bg_hisdept_vs_bgdept where bg_dept_id != #{deptId}
    </delete>


    <insert id="insertBatchMergedData" parameterType="java.util.List">
        insert into budget.bg_hisdept_vs_bgdept(his_dept_id,his_dept_name,bg_dept_id,bg_dept_name,hospital_id,his_org_id,his_update_time) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
            (
            #{tmpData.hisDeptId}, #{tmpData.hisDeptName},#{tmpData.bgDeptId},#{tmpData.bgDeptName},#{tmpData.hospitalId},#{tmpData.hisOrgId},now()
            )
        </foreach >
    </insert>

    <!-- 根据审核科室删除科目与科室对照关系-->
    <delete id="deleteBgDeptVsExpendByDeptId">
        delete from budget.bg_dept_vs_expendcode where dept_id = #{deptId} and add_vs_check =#{addVsCheck} and expend_code = #{expendCode}
    </delete>

    <!-- 插入科目与科室对照关系-->
    <insert id="insertBgDeptVsExpend" parameterType="java.util.List">
        insert into budget.bg_dept_vs_expendcode(id,dept_id,expend_code,add_vs_check,his_update_time) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
            (
            #{tmpData.id}, #{tmpData.deptId},#{tmpData.expendCode},#{tmpData.addVsCheck},now()
            )
        </foreach >

    </insert>

    <!-- 删除审核科室与审核人员对照-->
    <delete id="deleteBgDeptVsUserByDeptId">
        delete from budget.bg_dept_vs_user where dept_id = #{deptId} and expend_code = #{expendCode} and user_account_id = #{userAccountId}
    </delete>

    <!-- 插入审核科室与审核人员对照关系-->
    <insert id="insertBgDeptVsUser" parameterType="java.util.List">
        insert into budget.bg_dept_vs_user(id,dept_id,expend_code,user_account_id,add_vs_check,his_update_time) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
            (
            #{tmpData.id}, #{tmpData.deptId},#{tmpData.expendCode},#{tmpData.userAccountId},#{tmpData.addVsCheck},now()
            )
        </foreach >

    </insert>

    <!-- 删除审核科室范围表-->
    <delete id="deleteBgDeptVsDeptByDeptId">
        delete from budget.bg_dept_vs_dept where dept_id = #{deptId} and audit_expend_code = #{expendCode} and user_account_id = #{userAccountId}
    </delete>
    <delete id="deleteApplyOrCheckBgDeptVsExpend" parameterType="java.lang.String">
       delete from budget.bg_dept_vs_expendcode where add_vs_check =#{addVsCheck}
    </delete>

    <!-- 插入审核科室范围-->
    <insert id="insertBgDeptVsDept" parameterType="java.util.List">
        insert into budget.bg_dept_vs_dept(id,dept_id,audited_dept_id,audit_expend_code,add_vs_check,user_account_id,his_update_time) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
            (
            #{tmpData.id}, #{tmpData.deptId},#{tmpData.auditedDeptId},#{tmpData.auditExpendCode},#{tmpData.addVsCheck},#{tmpData.userAccountId},now()
            )
        </foreach >

    </insert>

</mapper>