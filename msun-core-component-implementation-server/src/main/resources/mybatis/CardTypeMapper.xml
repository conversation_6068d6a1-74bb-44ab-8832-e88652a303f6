<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.importdata.dao.CardTypeMapper">

    <select id="getCardType"
            resultType="com.msun.core.component.implementation.server.importdata.entity.po.CardType">

       SELECT card_type_id as cardTypeId,card_length as cardLength FROM comm."card_type" where invalid_flag='0' and card_length is not null
    </select>

</mapper>