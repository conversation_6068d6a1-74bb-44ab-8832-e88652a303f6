<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.cbhs.dao.CbhsCostAccountMapper">

    <insert id="insertCbhsDept" parameterType="java.util.List">
        insert into costaccount.ca_set_dept(dept_id, dept_name,parent_dept_id,dept_type,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR}, #{item.deptName,jdbcType=VARCHAR}, #{item.parentDeptId,jdbcType=VARCHAR},
            #{item.deptType,jdbcType=CHAR},#{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},
            #{item.hisUpdaterId},#{item.hisUpdateTime},#{item.version},#{item.hospitalId})
        </foreach>
    </insert>
    <insert id="insertCbhsDeptMap" parameterType="java.util.List">
        insert into costaccount.d_costaccount_dept_map2 (old_id, old_name, mz_id,mz_name,zy_id,zy_name,change_type,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR}, #{item.deptName,jdbcType=VARCHAR}, #{item.mzId,jdbcType=VARCHAR},
            #{item.mzName,jdbcType=VARCHAR}, #{item.zyId,jdbcType=VARCHAR},#{item.zyName,jdbcType=VARCHAR},
            #{item.changeType,jdbcType=VARCHAR},#{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId})
        </foreach>
    </insert>
    <insert id="insertCbhsDeptType" parameterType="java.util.List">
        insert into costaccount.ca_set_cost_type(COST_TYPE_ID, COST_TYPE_NAME, PARENT_COST_TYPE_ID,is_leaf,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.costTypeId,jdbcType=VARCHAR}, #{item.costTypeName,jdbcType=VARCHAR}, #{item.parentCostTypeId,jdbcType=VARCHAR},#{item.isLeaf,jdbcType=VARCHAR},
            #{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId})
        </foreach>
    </insert>
    <insert id="insertBatchDeptShare">
        INSERT INTO costaccount.ca_dept_share_config(dept_id, share_flag, force_share_flag, share_type_id,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId}, #{item.shareFlag}, #{item.forceShareFlag},#{item.shareTypeId},#{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId})
        </foreach>
    </insert>
    <insert id="insertBatchSharePara">
        INSERT INTO costaccount.ca_dept_share_para(dept_id, para_id, factor_ratio,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId}, #{item.paraId}, #{item.factorRatio},#{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId})
        </foreach>
    </insert>
    <insert id="batchInsertShareLevel">
        insert into costaccount.ca_dept_share_level(DEPT_ID,SHARE_LEVEL_ID,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id) VALUES
        <foreach collection ="list" item="data" separator =",">
            (#{data.deptId},#{data.shareLevelId},#{data.hisOrgId},#{data.hisCreaterId},#{data.hisCreaterName},#{data.hisCreateTime},#{data.hisUpdaterId},
            #{data.hisUpdateTime},#{data.version},#{data.hospitalId})
        </foreach >
    </insert>
    <insert id="addSystemParam">
        insert into costaccount.system_param (param_name,param_key,param_value,is_system)
        values (#{paramName},#{paramKey},#{paramValue},#{isSystem})
    </insert>
    <insert id="insertIncomeTypeBatch" parameterType="java.util.List">
        insert into costaccount.ca_set_income_type (income_type_id, income_type_name, parent_income_type_id,leaf,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.incomeTypeId,jdbcType=VARCHAR}, #{item.incomeTypeName,jdbcType=VARCHAR}, #{item.parentIncomeTypeId,jdbcType=VARCHAR},#{item.leaf,jdbcType=VARCHAR},
            #{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId}
            )
        </foreach>
    </insert>
    <insert id="insertDataCollect">
        insert into costaccount.ca_set_data_collect(col_code, col_name, col_value,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id)
        values (#{colCode}, #{colName}, #{colValue},#{hisOrgId},#{hisCreaterId},#{hisCreaterName},#{hisCreateTime},#{hisUpdaterId},
            #{hisUpdateTime},#{version},#{hospitalId})
    </insert>
    <insert id="insertFinanceDeptBatch" parameterType="java.util.List">
        insert into costaccount.ca_set_finance_dept(dept_id,dept_name,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR}, #{item.deptName,jdbcType=VARCHAR}, #{item.hisOrgId},#{item.hisCreaterId},#{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},
            #{item.hisUpdateTime},#{item.version},#{item.hospitalId}
            )
        </foreach>
    </insert>
    <insert id="insertHisDeptBatch">
        insert into costaccount.ca_daq_set_dept(dept_id,dept_name,dept_source,his_org_id,his_creater_id,
        his_creater_name,his_create_time,his_updater_id,his_update_time,version,hospital_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR}, #{item.deptName,jdbcType=VARCHAR}, #{item.deptSource,jdbcType=VARCHAR},  #{item.hisOrgId},#{item.hisCreaterId},
            #{item.hisCreaterName},#{item.hisCreateTime},#{item.hisUpdaterId},#{item.hisUpdateTime},#{item.version},#{item.hospitalId}
            )
        </foreach>
    </insert>
    <update id="updateSystemParam">
        update costaccount.system_param set param_value = #{paramValue} where param_key = #{paramKey}
    </update>
    <update id="updateDataCollect">
        update costaccount.ca_set_data_collect set col_value = #{colValue},his_update_time = #{hisUpdateTime} where col_code = #{colCode} and hospital_id = #{hospitalId}
    </update>
    <update id="updateDeptLeafFlag">
    update costaccount.ca_set_dept set leaf_flag = (
        select case when dept_id in (
            select csd1.dept_id
            from costaccount.ca_set_dept csd1
            left join costaccount.ca_set_dept csd2
            on csd1.dept_id = csd2.parent_dept_id and csd2.hospital_id = #{hospitalId}
            where csd2.dept_id is null and csd1.hospital_id = #{hospitalId}
        ) then '1' else '0' end
    ),his_update_time = now() where hospital_id = #{hospitalId}
    </update>
    <delete id="deleteCbhsDeptAll">
        delete from costaccount.ca_set_dept where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteCbhsDeptMapAll">
        delete from costaccount.d_costaccount_dept_map2 where hospital_id = #{hospitalId} and change_type = #{changeType}
    </delete>
    <delete id="deleteCbhsDeptType">
        delete from costaccount.ca_set_cost_type where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteAllShareConfig">
        delete from costaccount.ca_dept_share_config where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteDeptSharePara">
        delete from costaccount.ca_dept_share_para where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteByShareLevel">
        delete from costaccount.ca_dept_share_level where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteAllIncomeType">
        delete from costaccount.ca_set_income_type where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteFinanceDeptInfo">
        delete from costaccount.ca_set_finance_dept where hospital_id = #{hospitalId}
    </delete>
    <delete id="deleteHisDeptInfo">
        delete from costaccount.ca_daq_set_dept where dept_source = #{deptSource} and hospital_id = #{hospitalId}
    </delete>
    <select id="selectSkillDeptList"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CbhsCaSetDeptPo">
        select dept_id,dept_name,parent_dept_id,index_id,pinyin_code,dept_type,use_flag,dept_class,show_flag,description,leaf_flag
        from costaccount.ca_set_dept csd
        where csd.hospital_id = #{hospitalId} and csd.parent_dept_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectLeafDeptList"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CbhsCaSetDeptPo">
        select dept_id,dept_name,parent_dept_id,dept_type,leaf_flag
        from costaccount.ca_set_dept csd
        where leaf_flag = '1' and hospital_id = #{hospitalId}
    </select>
    <select id="selectSystemParamByKey"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CbhsSystemParam">
        select id,param_value from costaccount.system_param where param_key = #{paramKeys}
    </select>
    <select id="selectByColCode"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CaSetDataCollectPo">
        select
          id, col_code, col_name, col_value, col_order, col_status, remark, db_code
        from costaccount.ca_set_data_collect
        where  col_code = #{colCode} and hospital_id = #{hospitalId}
    </select>
    <select id="selectInvoiceIncomeType"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CbhsSetIncomeType">
        select invoice_item_code income_type_id,invoice_item_name income_type_name from dwd.dwd_dict_invoice_item where invalid_flag_id = '0'
    </select>
    <select id="selectCalculateIncomeType"
            resultType="com.msun.core.component.implementation.server.cbhs.po.CbhsSetIncomeType">
        select account_item_code income_type_id,account_item_name income_type_name from dwd.dwd_dict_account_item where invalid_flag_id = '0'
    </select>
    <select id="judgeIsExistCostAccountDept" resultType="java.lang.Integer">
        select 1
        from  costaccount.ca_set_dept
        where hospital_id = #{hospitalId}
        limit 1
    </select>
    <select id="judgeIsExistIncomeType" resultType="java.lang.Integer">
        select 1
        from  costaccount.ca_set_income_type
        where hospital_id = #{hospitalId}
        limit 1
    </select>
    <select id="judgeIsExistCostType" resultType="java.lang.Integer">
        select 1
        from  costaccount.ca_set_cost_type
        where hospital_id = #{hospitalId}
        limit 1
    </select>
    <select id="judgeIsExistDeptMap" resultType="java.lang.Integer">
        select 1
        from  costaccount.d_costaccount_dept_map2
        where hospital_id = #{hospitalId} and change_type = #{deptType}
        limit 1
    </select>
    <select id="judgeIsExistFinance" resultType="java.lang.Integer">
        select 1
        from  costaccount.ca_set_finance_dept
        where hospital_id = #{hospitalId}
        limit 1
    </select>
</mapper>