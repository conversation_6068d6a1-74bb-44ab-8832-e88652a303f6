<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.cssd.dao.CodeSerialsMapper">

    <resultMap type="com.msun.core.component.implementation.server.cssd.entity.po.CodeSerialsPO" id="CodeSerialsMap">
        <result property="codeId" column="code_id" jdbcType="INTEGER"/>
        <result property="codeType" column="code_type" jdbcType="VARCHAR"/>
        <result property="codeName" column="code_name" jdbcType="VARCHAR"/>
        <result property="codePrefix" column="code_prefix" jdbcType="VARCHAR"/>
        <result property="codeLength" column="code_length" jdbcType="INTEGER"/>
        <result property="codeNum" column="code_num" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="updateFlagDay" column="update_flag_day" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="isCloudDate" column="is_cloud_date" jdbcType="TIMESTAMP"/>
    </resultMap>
    <!--根据code查询单据生成规则-->
    <select id="selectByCode" resultType="com.msun.core.component.implementation.server.cssd.entity.po.CodeSerialsPO" parameterType="java.lang.String">
        select code_id as codeId,
               code_type as codeType,
               code_name as codeName,
               code_prefix as codePrefix,
               code_length as codeLength,
               code_num as codeNum,
               invalid_flag as invalidFlag,
               update_flag_day as updateFlagDay,
               version as version,
               his_org_id as hisOrgId,
               hospital_id as hospitalId,
               his_creater_id as hisCreaterId,
               his_creater_name as hisCreaterName,
               his_create_time as hisCreateTime,
               is_cloud_date as isCloudDate
        from cssd.code_serials
        where 1=1 and code_type=#{code}
    </select>

    <update id="updateCodeSerial" parameterType="com.msun.core.component.implementation.server.cssd.entity.po.CodeSerialsPO" keyColumn="codeNum" keyProperty="codeNum">
        update cssd.code_serials set code_num=code_num+1 ,his_update_time = now() where code_type=#{codeType};
        <selectKey resultType="int" keyProperty="codeNum" order="AFTER">
            select code_num as codeNum from cssd.code_serials where 1=1 and code_type=#{codeType};
        </selectKey>
    </update>

    <update id="updateCodeNum">
        update cssd.code_serials set code_num=0 ,his_update_time = now() where 1=1 and update_flag_day='1';
    </update>

</mapper>
