<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.emr.dao.ConfigMapper" >
    <sql id="baseSql">
        c.config_id as configId,
        c.config_code as configCode,
        c.config_name as configName,
        c.business_type_id as businessTypeId,
        c.precondition as precondition,
        c.config_type as configType,
        c.sort_order as sortOrder,
        c.display_flag as displayFlag,
        c.maintain_flag as maintainFlag,
        c.config_value as configValue,
        c.memo as memo,
        c.version as version
    </sql>

    <!--根据配置编码批量获取配置信息-->
    <select id="getConfigByCode" resultType="com.msun.core.component.implementation.server.emr.po.Config">
        select
        <include refid="baseSql"/>
        from
        comm.config c
        where
        c.config_code=#{code}
        limit 1
    </select>

    <select id="findNoHandWriting"  resultType= "Integer">
        SELECT
            count (1)
        FROM
            comm."user" u
            LEFT JOIN comm.IDENTITY i ON i.user_id = u.user_id
            LEFT JOIN comm.ROLE r ON i.identity_id = r.role_id
        WHERE
            r.role_type = '7'
            and u.hospital_id = #{hospitalId}
            AND u.red_signature IS NULL
            AND u.black_signature IS NULL;
    </select>


    <select id="getOld2NewTemplatesByHospitalId" resultType="java.lang.Integer">
        select count(1) from emr.emr_mr_templates
        where hospital_id = #{hospitalId}
        and visible='1'
        and template_content is not null
        and convert_flag='1'
        and (his_creater_name='system' or his_creater_name='system_know')
    </select>

    <select id="getOutHospitalFunctionOrder" resultType="java.lang.Integer">
        select count(1) from comm.dict_order
        where hospital_id = #{hospitalId}
        and order_category_id in(9000,9004,9005)
        and invalid_flag='0'
    </select>
</mapper>
