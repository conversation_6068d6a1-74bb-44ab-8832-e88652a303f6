<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.cssd.dao.CssdDictDeptMapper">


    <update id="cancelIsCssd">
        update cssd.c_dept set is_cssd = 0,his_update_time = now() where is_cssd = 1 and hospital_id = #{hospitalId}
    </update>

    <!--查询管理科室ID-->
    <select id="selectByHospital" resultType="com.msun.core.component.implementation.server.cssd.entity.po.CssdDictDeptPO">
        select * from cssd.c_dept where is_cssd = 1 and hospital_id = #{hospitalId}
    </select>

    <!--查询配置-->
    <select id="selectConfig" resultType="com.msun.core.component.implementation.api.cssd.dto.CssdConfigDTO">
        select value configValue from cssd.material_config where key='skipProcessList'
    </select>

</mapper>
