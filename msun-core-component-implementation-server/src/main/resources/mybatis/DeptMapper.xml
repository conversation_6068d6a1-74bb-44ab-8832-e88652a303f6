<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DeptMapper">
    <insert id="addConfigPharmacyDept">
        insert
        into
            comm.config_pharmacy_dept (
            dept_id,
            secondary_flag,
            infusion_flag,
            invalid_flag,
            hospital_id,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            "version",
            call_number_flag,
            med_mail_flag,
            share_chinese_pharmacy_flag)
        select
            dept_id,
            '0' secondary_flag,
            '0' infusion_flag,
            '0' invalid_flag,
            hospital_id,
            his_org_id,
            -1 his_creater_id,
            his_creater_name,
            his_create_time,
            -1 his_updater_id,
            his_update_time,
            '0' "version",
            '0' call_number_flag,
            '0' med_mail_flag,
            '0' share_chinese_pharmacy_flag
        from
            comm.dept
        where
            category_id = '4'
          and invalid_flag = '0'
          and hospital_id = #{hospitalId}
          and dept_id not in (
            select
                dept_id
            from
                comm.config_pharmacy_dept
            where
                hospital_id = #{hospitalId}
        )

    </insert>
    <insert id="addConfigStorageDept">

        insert
        into
            comm.config_storage_dept (
            dept_id,
            drug_class,
            invalid_flag,
            hospital_id,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            "version",
            share_chinese_storage_flag
        )

        select
            dept_id,
            '' drug_class,
            '0' invalid_flag,
            hospital_id,
            his_org_id,
            -1 his_creater_id,
            his_creater_name,
            his_create_time,
            -1 his_updater_id,
            his_update_time,
            '0' "version",
            '0' share_chinese_storage_flag

        from
            comm.dept
        where
            category_id = '5'
          and invalid_flag = '0'
          and hospital_id = #{hospitalId}
          and dept_id not in (
            select
                dept_id
            from
                comm.config_storage_dept
            where
                hospital_id = #{hospitalId}
        )
    </insert>
    <insert id="addConfigTechDept">

        insert
        into
        comm.config_tech_dept
            (
            config_tech_dept_id,
            dept_id,
            category_id,
            invalid_flag,
            hospital_id,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            "version",
            transfusion_flag
        )
        select
            dept_id config_tech_dept_id,
            dept_id,
            category_id,
            '0' invalid_flag,
            hospital_id,
            his_org_id,
            -1 his_creater_id,
            his_creater_name,
            his_create_time,
            -1 his_updater_id,
            his_update_time,
            '0' "version",
            '0' transfusion_flag

        from
            comm.dept
        where
            category_id = '6'
          and invalid_flag = '0'
          and hospital_id = #{hospitalId}
          and dept_id not in (
            select
                dept_id
            from
                comm.config_tech_dept
            where
                hospital_id = #{hospitalId}
        )

    </insert>
    <insert id="addConfigNurseDept">
        insert
        into
            comm.config_nurse_dept (
            config_nurse_dept_id,
            dept_id,
            invalid_flag,
            hospital_id,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            "version",
            emergency_dept_flag,
            appointment_discharge_flag
        )
        select
            dept_id config_nurse_dept_id,
            dept_id,
            '0' invalid_flag,
            hospital_id,
            his_org_id,
            -1 his_creater_id,
            his_creater_name,
            his_create_time,
            -1 his_updater_id,
            his_update_time,
            '0' "version",
            '0' emergency_dept_flag,
            '1' appointment_discharge_flag

        from
            comm.dept
        where
            category_id = '9'
          and invalid_flag = '0'
          and hospital_id = #{hospitalId}
          and dept_id not in (
            select
                dept_id
            from
                comm.config_nurse_dept
            where
                hospital_id = #{hospitalId}
        )
    </insert>

    <select id="getColumns"
            resultType="com.msun.core.component.implementation.server.imsp.entity.vo.SchemaColumnsVo">

        select distinct
            cs.table_name,
            cs.column_name,
            cs.udt_name AS data_type,
            0 AS length,
            case when cs.is_nullable ='YES' THEN 'f' else 't' end AS notnull
        from information_schema.COLUMNS cs
        where 1 = 1
        <if test="tableSchema != null and tableSchema != '' ">
            and cs.table_schema  = #{tableSchema}
        </if>
        AND cs.TABLE_NAME = #{tableName}
    </select>

    <select id="findByDeptNameSet" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
        select
        a.dept_id,
        a.dept_code,
        a.category_id,
        a.dept_name
        from comm.dept a
        <where>
            a.dept_name in
            <foreach collection="deptNameSet" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            and a.invalid_flag = '0'
            and a.hospital_id = #{hospitalId}
        </where>
    </select>

    <!--根据表查询数据类型-->
    <select id="getColumnsChisApp"
            resultType="com.msun.core.component.implementation.server.imsp.entity.vo.SchemaColumnsVo">
        select distinct
        cs.table_name,
        cs.column_name,
        cs.udt_name AS data_type,
        0 AS length,
        case when cs.is_nullable ='YES' THEN 'f' else 't' end AS notnull
        from information_schema.COLUMNS cs
        where 1 = 1
        <if test="tableSchema != null and tableSchema != '' ">
            and cs.table_schema  = #{tableSchema}
        </if>
        AND cs.TABLE_NAME = #{tableName}

    </select>

    <!--根据表查询数据类型-->
    <select id="getColumnsCdrapp"
            resultType="com.msun.core.component.implementation.server.imsp.entity.vo.SchemaColumnsVo">
        select distinct
        cs.table_name,
        cs.column_name,
        cs.udt_name AS data_type,
        0 AS length,
        case when cs.is_nullable ='YES' THEN 'f' else 't' end AS notnull
        from information_schema.COLUMNS cs
        where 1 = 1
        <if test="tableSchema != null and tableSchema != '' ">
            and cs.table_schema  = #{tableSchema}
        </if>
        AND cs.TABLE_NAME = #{tableName}

    </select>

    <!--查询是药房的数据-->
    <select id="getDeptPharmacyList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
    select
       dept_id,
       dept_name,
       dept_code
     from comm.dept
     <where>
     <!-- and his_org_id = #{hisOrgId} -->
         hospital_id = #{hospitalId}
         and category_id='4'
         and invalid_flag = '0'
     </where>
    </select>
    <!--根据名称查询id-->
    <select id="getRecipeId" resultType="java.lang.Long">
       select recipe_id from  comm.recipe where recipe_name=#{recipeName}
    </select>

    <select id="selectallDept" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
        select dept_id as deptId,dept_name as deptName from  comm.dept
    </select>

    <select id="findPharmacyDept"
            resultType="com.msun.core.component.implementation.api.autotest.vo.AutoTestPharmacyDeptInfoVO">
        select d.dept_id as id , d.dept_name  as name
        FROM comm.dept d join comm.dept_vs_category dvc on d.dept_id = dvc.dept_id
                         join comm.config_pharmacy_dept cpd on d.dept_id = cpd.dept_id
        where dvc.category_id = '4'
          and cpd.secondary_flag = '0'
          and d.invalid_flag = '0'
          and d.hospital_id in
            <foreach collection="hospitalIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>