<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictAccountItemMapper">
    <select id="selectAccountList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictAccountItem">
        select
            account_item_id as accountItemId,
            account_item_code as accountItemCode,
            account_item_name as accountItemName,
            input_code as inputCode,
            wb_code as wbCode
        from
            comm.dict_account_item
        where
            invalid_flag = '0'
            and (
                his_org_id = -1
            or
                his_org_id = #{hisOrgId}
            )
    </select>

</mapper>