<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictChargeClassMapper">
<!--查询数据-->
    <select id="getDictChargeClassList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictChargeClassVo">
    select
    *
    from comm.dict_charge_class
    where invalid_flag='0'
    AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})

    </select>
</mapper>