<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictChargePriceMapper">

    <select id="selectChargeByExecChargeId"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictChargePrice">
        select charge_id,charge_name,input_code,price,full_code from comm.dict_charge_price where
        msun_standard_id = #{execChargeId}
        and his_org_id = #{orgId}
        and hospital_id = #{hospitalId}
    </select>

    <select id="selectOfCount" resultType="double">
        select count (charge_id)
        from comm.dict_charge_price
        where
        hospital_id = #{hospitalId}
    </select>
    <!--根据医院的id查询出全部的医嘱id-->
    <select id="selectAllOrderId" resultType="com.msun.core.component.implementation.api.imsp.dto.MedicalOrdersDTO">
       select dict_order_id,
               order_code,
               order_name,
               order_class,
               input_code,
               full_code,
               continue_flag,
               price,
               order_unit_id,
               order_unit_name,
               his_org_id
        from comm.dict_order
        where hospital_id = #{hospitalId}
        and invalid_flag =#{invalidFlag}
    </select>
    <!--查询护嘱数据-->
    <!--    <select id="selectDictNurseOrderPOMap" resultType="java.util.Map">-->
    <!--      select * from-->
    <!--        comm.dict_nurse_order-->
    <!--       where  and his_org_id = #{orgId}-->
    <!--              and hospital_id = #{hospitalId}-->
    <!--    </select>-->
    <!--查询医嘱字典与护嘱字典表对应关系-->
    <!--    <select id="selectDictOrderDocVsNursePOMap" resultType="java.util.Map">-->
    <!--        select * from-->
    <!--        comm.dict_order_doc_vs_nurse-->
    <!--        where  and his_org_id = #{orgId}-->
    <!--              and hospital_id = #{hospitalId}-->
    <!--    </select>-->
    <!--查询医嘱计价（护嘱）-->
    <!--    <select id="selectDictOrderVsChargeMap" resultType="java.util.Map">-->
    <!--       select * from-->
    <!--          comm.dict_order_vs_charge-->
    <!--        where  and his_org_id = #{orgId}-->
    <!--              and hospital_id = #{hospitalId}-->
    <!--    </select>-->
    <!--查询医院全部的计价费用-->
    <!--   <select id="selectDictChargePriceDTOMap" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictChargePrice">
          select * from
             comm.dict_charge_price
           where 1=1
            and his_org_id = #{hisOrgId}
            and hospital_id = #{hospitalId}
            and invalid_flag='0'
       </select>-->
    <!--查询计价所有的条数-->
    <select id="getTotalCount" resultType="int">
      select count(*)  from
          comm.${tableName}
        where 1=1
         and hospital_id = #{hospitalId}
         and invalid_flag='0'
    </select>
    <!--查询计价超过10000条数据-->
    <select id="dictChargePriceData"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictChargePrice">
         select * from
          comm.${tableName}
          where 1=1
         and hospital_id = #{hospitalId}
         and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>
    <!--查询计价超过10000条数据-->
    <select id="dictOrderData" resultType="com.msun.core.component.implementation.api.imsp.dto.MedicalOrdersDTO">
        select * from
            comm.${tableName}
        where 1=1
          and hospital_id = #{hospitalId}
          and invalid_flag = '0'
            limit #{max_value}  offset  #{limit}
    </select>
    <!--根据名称查询功能医嘱的OrderCategoryId-->
    <select id="queryOrderCategoryId" resultType="long" parameterType="map">
        select
        order_category_id
        from comm.dict_order_category
        where order_category_name=#{orderCategoryName}
        <!--and his_org_id = #{hisOrgId}-->
    </select>
    <!--医嘱分类表查询所有数据-->
    <select id="getOrderCategoryList" resultType="com.msun.core.component.implementation.api.imsp.dto.OrderCategoryDTO">
        select
        order_category_id,
        order_category_name
        from comm.dict_order_category
        where 1=1
        <!--and his_org_id = #{hisOrgId}-->
    </select>
    <select id="getDictChargePriceData"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictChargePriceVo">
        select
        charge_id,
        charge_code,
        charge_name,
        input_code,
        price,
        spec,
        measure_unit_id,
        measure_unit_name,
        start_time,
        stop_time,
        order_no,
        invoice_item_id,
         account_item_id,
         med_rec_item_id,
         med_insurance_item_id,
         price_modify_flag,
         out_flag,
         in_flag,
          nurse_flag,
          sub_flag,
          parent_flag,
          exe_dept_id,
          special_type,
          parent_charge_id,
           memo,
           med_insurance_code,
           full_code,
           invalid_flag,
           his_org_id,
           his_creater_id,
           his_creater_name,
           his_create_time,
           his_updater_id,
           his_update_time,
          version,
          order_category_id,
          tj_flag,
          wb_code,
          disposable,
          implant,
          takeout,
          producer_id,
          imported,
          invalid_time,
          producer_cnname,
           national_insurance_code,
           source_flag,
           msun_standard_id,
           provincial_insurance_code,
           insurance_catalog_name,
           max_limit_number,
           provincial_insurance_name,
           special_medical_services,
           pre_invalid_time,
           delivery_flag,
           is_only_self_pay,
           doc_consult_flag,
           nurse_consult_flag,
           item_meaning,
           description,
           exclusions,
           medinsur_price_limit,
           prefix,
           suffix,
           register_number,
           change_memo,
           second_account_item,
           pre_valid_time,
           valid_time,
           remote_order_id,
           dict_item_type_id,
           pre_time_updater_id
          from
          comm.${tableName}
          where 1=1
         and hospital_id = #{hospitalId}
         and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>
    <!--查询医嘱数据-->
    <select id="getDictOrderList" resultType="com.msun.core.component.implementation.api.imsp.vo.DictOrderVo">
       SELECT
       dict_order_id,
       order_code,
       order_name,
       order_class,
       input_code,
       full_code,
       out_flag,
       clinic_flag,
       continue_flag,
       st_flag,
       edit_flag,
       price,
       modify_flag,
       edit_type,
        memo,
        spec,
         order_no,
         order_select_limit,
         order_unit_id,
         order_unit_name,
         his_org_id,
         version,
         invalid_flag,
          exe_dept_id,
          wb_code,
          exe_dept_name,
          order_category_id,
           tj_flag,
           his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
             his_update_time,
              hospital_id,
               decimal_flag,
                apply_form_flag,
                xy_flag,
                only_singel_flag,
                order_old_id,
                national_medical_insurance_code,
                source_flag,
                appoint_flag,
                nurse_class_id,
                max_limit_number,
                provincial_insurance_code,
                 provincial_insurance_name,
                 dict_order_type_id,
                 special_flag,
                 exe_hospital_id,
                  bill_exe_flag,
                  insurance_catalog_name,
                   support_anes,
                   gender_limit,
                   is_only_self_pay,
                    emergency_flag,
                     share_flag,
                     item_meaning,
                     description,
                     exclusions,
                      medinsur_price_limit,
                      remote_order_id,
                      remote_hospital_id
          from
          comm.${tableName}
          where 1=1
         and hospital_id = #{hospitalId}
         and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>
    <select id="selectListChargePage" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictChargePrice">
       select * from
         comm.dict_charge_price
          where  hospital_id = #{hospitalId}
         and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>


</mapper>