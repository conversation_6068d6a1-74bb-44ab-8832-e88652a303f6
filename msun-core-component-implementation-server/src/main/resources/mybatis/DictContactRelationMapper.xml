<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictContactRelationMapper">

    <!-- 查询机构下所有联系人数据-->
    <select id="selectByHosOrgId"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictContactRelationPo">
        SELECT
            *
        FROM
            comm.dict_contact_relation
        WHERE
           <!--his_org_id = #{his_org_id}-->
          invalid_flag='0'
    </select>
</mapper>
