<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.DictDrugMajorControlBatchMapper">


    <insert id="insertSaveData">
        insert into comm.dict_drug_major_control_batch (
            major_control_batch_id   ,
            major_control_batch_name ,
            major_control_flag       ,
            data_source              ,
            input_code               ,
            full_code                ,
            wb_code                  ,
            order_no                 ,
            invalid_flag             ,
            hospital_id              ,
            his_org_id               ,
            his_creater_id           ,
            his_creater_name         ,
            his_create_time          ,
            his_updater_id           ,
            his_update_time          ,
            version

        )  values (#{majorControlBatchId},
                   #{majorControlBatchName},
                   #{majorControlFlag},
                   #{dataSource},
                   #{inputCode},
                   #{fullCode},
                   #{wbCode},
                   #{orderNo},
                   #{invalidFlag},
                   #{hospitalId},
                   #{hisOrgId},
                   #{hisCreaterId},
                   '管理员',
                   now(),
                   #{hisUpdaterId},
                   now(),
                   0
               )

    </insert>
</mapper>
