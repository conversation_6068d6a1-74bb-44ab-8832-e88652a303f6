<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictDrugMaterialQualityMapper">
    <!--查询包装材质字典-->
    <select id="selectMaterialQualityList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictMaterialQualityPo">
        select
            material_quality_id,
            material_quality_name,
            input_code,
            full_code,
            wb_code,
            invalid_flag
        from comm.dict_drug_material_quality
        where invalid_flag='0'
        and  (his_org_id = -1
            or
            his_org_id = #{hisOrgId})
    </select>
</mapper>