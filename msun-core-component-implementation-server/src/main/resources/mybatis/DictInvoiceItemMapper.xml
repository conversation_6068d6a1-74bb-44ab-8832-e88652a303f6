<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictInvoiceItemMapper">
    <select id="selectDictInvoiceItem"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictInvoiceItem">
        select invoice_item_id   as invoiceItemId,
               invoice_item_code as invoiceItemCode,
               invoice_item_name as invoiceItemName,
               input_code   as inputCode,
               full_code    as fullCode,
               invalid_flag as invalidFlag
        from comm.dict_invoice_item
        where invalid_flag = '0'
          and (his_org_id = -1 or his_org_id = #{hisOrgId})
    </select>
</mapper>