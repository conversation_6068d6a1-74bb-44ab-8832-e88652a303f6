<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictMeasureUnitMapper">
    <select id="selectDictMeasureUnit"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictMeasureUnit">
        select min(measure_unit_id)   as measureUnitId,
               measure_unit_name as measureUnitName,
               min(input_code)        as inputCode,
               min (full_code)         as fullCode,
               min(invalid_flag)      as invalidFlag
        from comm.dict_measure_unit
        where invalid_flag = '0'
          and measure_unit_name is not null
          and measure_unit_name != ''
          and (his_org_id = -1
            or
               his_org_id = #{hisOrgId})
               and invalid_flag = '0'
        group by measure_unit_name
    </select>

    <select id="selectDictMeasureUnitByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictMeasureUnit">
        SELECT
            measure_unit_id,
            measure_unit_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
        VERSION
        FROM
            comm.dict_measure_unit
        WHERE
            invalid_flag = '0'
            AND ( his_org_id = - 1 OR his_org_id = #{his_org_id} )

    </select>
</mapper>