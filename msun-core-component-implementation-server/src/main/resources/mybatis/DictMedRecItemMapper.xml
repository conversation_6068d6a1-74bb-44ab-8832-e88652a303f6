<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictMedRecItemMapper">
    <select id="selectDictMedRecItem" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictMedRecItem">
        select
            med_rec_item_id as medRecItemId,
            med_rec_item_code as medRecItemCode,
            med_rec_item_name as medRecItemName,
            input_code as inputCode,
            full_code as fullCode,
            invalid_flag as invalidFlag
        from
             comm.dict_med_rec_item
        where
            invalid_flag = '0'
           <!-- and
            (his_org_id = -1
            or
            his_org_id = #{hisOrgId})-->
    </select>
</mapper>