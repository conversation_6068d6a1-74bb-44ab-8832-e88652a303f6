<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictOrderCategoryMapper">

    <select id="selectDictOrderCategoryByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictOrderCategory">
        SELECT
            order_category_id,
            order_category_name,
            input_code,
            full_code,
            wb_code,
            out_doc_flag,
            in_doc_flag,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time
        FROM
            comm.dict_order_category
            where
             his_org_id = -1 or his_org_id = #{his_org_id}

    </select>
    <select id="selectAll"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictOrderCategory">
        SELECT
        order_category_id,
        order_category_name,
        input_code,
        full_code,
        wb_code,
        out_doc_flag,
        in_doc_flag,
        VERSION,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time
        FROM
        comm.dict_order_category
        where invalid_flag = '0'
        and order_category_name is not null
    </select>

    <select id="selectAllDictList"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictItemType">
        SELECT
            min(dict_item_type_id) as dict_item_type_id,
            dict_item_type_name
        FROM
        comm.dict_item_type
        where invalid_flag = '0'
        group by dict_item_type_name
    </select>
</mapper>