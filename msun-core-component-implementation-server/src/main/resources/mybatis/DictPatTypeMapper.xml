<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictPatTypeMapper">
    <!--查询重复数据-->
    <select id="getPatTypeRepetitionList" resultType="java.lang.String">
     select
       dict_pat_type_name
     from comm.dict_pat_type
      where invalid_flag='0'
      and his_org_id =#{hisOrgId}
    </select>
</mapper>