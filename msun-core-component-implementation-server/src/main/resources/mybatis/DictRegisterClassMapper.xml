<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DictRegisterClassMapper">

    <select id="selectDictRegisterClassByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictRegisterClass">
        SELECT
            register_class_id,
            register_class_name,
            input_code,
            full_code,
            order_no,
            invalid_flag,
            free_flag,
            default_recipe_id,
            wb_code,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            VERSION,
            hospital_id
        FROM
            comm.dict_register_class
        WHERE
            invalid_flag = '0'
            AND ( his_org_id = - 1 OR his_org_id = #{his_org_id} )
            AND hospital_id = #{hospital_id}
    </select>
</mapper>