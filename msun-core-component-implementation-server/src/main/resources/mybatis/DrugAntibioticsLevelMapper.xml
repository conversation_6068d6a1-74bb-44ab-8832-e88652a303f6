<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugAntibioticsLevelMapper">
    <select id="selectDrugAntibioticsLevelByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugAntibioticsLevel">

                SELECT
                    antibiotics_level_id,
                    antibiotics_level_name,
                    input_code,
                    full_code,
                    wb_code,
                    order_no,
                    invalid_flag,
                    VERSION,
                    his_org_id,
                    his_creater_id,
                    his_creater_name,
                    his_create_time,
                    his_updater_id,
                    his_update_time,
                    antibiotics_level_power
                FROM
                    comm.dict_drug_antibiotics_level
                WHERE
                    invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )标准字典去掉hospital_id，his_org_id-->
    </select>
</mapper>