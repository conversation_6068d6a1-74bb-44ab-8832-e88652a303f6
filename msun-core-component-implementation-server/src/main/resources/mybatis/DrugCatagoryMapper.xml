<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugCatagoryMapper">


    <select id="selectDrugCatagoryByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugCatagory">
        SELECT
        catagory_id,
        catagory_name,
        input_code,
        full_code,
        wb_code,
        order_no,
        invalid_flag,
        catagory_code,
        parent_id,
        VERSION,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        retail_price_ratio
        FROM
        comm.dict_drug_catagory
        WHERE
        invalid_flag = '0'
        <!-- AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )-->

    </select>

    <!--查询药材类别数据hisOrgId-->
    <select id="getDrugCatagoryOneList"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugCatagory">
        select
            distinct his_org_id
        FROM
        comm.dict_drug_catagory
        WHERE
        invalid_flag = '0'
    <!-- AND his_org_id = #{hisOrgId}-->
    and his_org_id  != '0'
    limit 1
</select>
<!--修改云健康药材类别数据hisOrgId-->
    <update id="updateDrugCatagoryHisOrgId">
     update comm.dict_drug_catagory
       set his_org_id =#{hisOrgId},
           his_create_time =#{hisCreateTime},
           his_update_time =#{hisCreateTime}
     WHERE
        invalid_flag = '0'
        and catagory_name =#{drugCatagoryName}
        and his_org_id = '0'
    </update>
</mapper>