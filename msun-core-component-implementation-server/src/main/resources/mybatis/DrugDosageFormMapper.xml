<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugDosageFormMapper">
    <select id="selectDrugDosageFormByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugDosageForm">
        SELECT
            dosage_form_id,
            dosage_form_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            dosage_form_code,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time
        FROM
            comm.dict_drug_dosage_form
        WHERE
            invalid_flag = '0'
            <!-- AND ( his_org_id = - 1 OR his_org_id = #{his_org_id} ) -->
    </select>
    <!--查询药品剂型字典信息-->
    <select id="getDrugDosageFormVoList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugDosageFormVo">
            SELECT
            dosage_form_name,
            dosage_form_code
        FROM
            comm.dict_drug_dosage_form
        WHERE
            invalid_flag = '0'
    </select>
</mapper>