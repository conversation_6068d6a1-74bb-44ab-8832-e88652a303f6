<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.DrugDoseUnitDddRecordMapper">

    <insert id="insertSaveData">
        insert into comm.drug_dose_unit_ddd_record
            (drug_dose_unit_ddd_record_id ,
             drug_id                      ,
             min_dose                     ,
             min_dose_unit_id             ,
             dose_unit_name               ,
             ddds_count                   ,
             invalid_flag                 ,
             hospital_id                  ,
             his_org_id                   ,
             his_creater_id               ,
             his_creater_name             ,
             his_create_time              ,
             his_updater_id               ,
             his_update_time              ,
             version                      )
        values(#{drugDoseUnitDddRecordId},
               #{drugId},
               #{minDose},
               #{minDoseUnitId},
               #{doseUnitName},
               #{dddsCount},
               #{invalidFlag},
               #{hospitalId},
               #{hisOrgId},
               #{hisCreaterId},
               '管理员',
               now(),
               #{hisUpdaterId},
               now(),
               0
               )
    </insert>
</mapper>
