<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugFrequencyMapper">

    <select id="selectDrugFrequencyByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugFrequency">
        SELECT
            frequency_id,
            frequency_name,
            input_code,
            other_code,
            full_code,
            invalid_flag,
            count_everytime,
            memo,
            flag_show,
            exec_days,
            frequency_type,
            order_no,
            in_use_range,
            infusion_time,
            default_exec_time_id,
            infusion_priority,
            infusion_interval,
            flag_frequency_en,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            cron,
            VERSION,
            hospital_id
        FROM
            comm.dict_frequency
        WHERE
            invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )标准字典去掉hospital_id，his_org_id-->

    </select>
</mapper>