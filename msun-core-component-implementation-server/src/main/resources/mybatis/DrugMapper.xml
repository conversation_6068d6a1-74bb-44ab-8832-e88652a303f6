<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.DrugBasicMainMapper">
    <sql id="baseSql">
        a
        .
        drug_basic_main_id
        ,
        a.drug_code,
        a.drug_name,
        a.drug_alias_name,
        a.spec_memo,
        a.dosage_form_id,
        a.producer_id,
        a.supplier_id,
        a.drug_catagory_id,
        a.source_flag,
        a.approved_no,
        a.pharm_code,
        a.effect_type_id,
        a.acid_base_id,
        a.spec_code,
        a.material_quality_id,
        a.min_packing_id,
        a.min_preparation_id,
        a.night_essential_drug_flag,
        a.night_essential_drug_code,
        a.twelve_essential_drug_flag,
        a.twelve_essential_drug_code,
        a.twelve_essential_drug_name,
        a.essential_drug_id,
        a.min_dose_unit_id,
        a.dose_unit_id,
        a.min_dose,
        a.drug_use_unit_id,
        a.antibiotics_level_id,
        a.antibiotics_type_id,
        a.toxicity_id,
        a.filling_method_id,
        a.usage_type_id,
        a.frequency_id,
        a.indication_id,
        a.component,
        a.ndc_id,
        a.ddd,
        a.ddd_unit,
        a.max_single,
        a.max_single_unit_id,
        a.route_id,
        a.taboo_id,
        a.invoice_id,
        a.medical_record_id,
        a.accounting_item_id,
        a.medical_insurance_id,
        a.image,
        a.version,
        a.invalid_flag,
        a.input_code,
        a.full_code,
        a.wb_code,
        a.his_org_id,
        a.his_creater_id,
        a.his_creater_name,
        a.his_create_time,
        a.his_updater_id,
        a.his_update_time,
        a.producer_name,
        a.pharmacy_id,
        a.default_unit_id,
        a.produce_area_id,
        a.memo,
        a.save_type_id
    </sql>
    <sql id="branchSql">
        b
        .
        drug_basic_branch_id
        ,
        b.antitumor_flag,
        b.poison_flag,
        b.narcosis_flag,
        b.radiate_flag,
        b.china_drug_injection_flag,
        b.gut_nutrition_flag,
        b.hormone_flag,
        b.major_control_flag,
        b.medical_insurance_flag,
        b.antibiotics_flag,
        b.Import_patent_flag,
        b.original_flag,
        b.low_price_flag,
        b.fixed_flag,
        b.maternity_flag,
        b.pediatrics_flag,
        b.rescue_flag,
        b.blood_flag,
        b.spirit_one_flag,
        b.spirit_two_flag,
        b.precious_flag,
        b.skin_test_flag,
        b.skin_test_type_id,
        b.skin_test_order_type_id,
        b.vein_nutrition_flag,
        b.test_drug_flag,
        b.chemotherapy_flag,
        b.hard_calm_flag,
        b.self_make_flag,
        b.sensitivity_flag,
        b.outpatient_flag,
        b.inpatient_flag,
        b.village_flag,
        b.assist_flag,
        b.high_risk_flag
    </sql>
    <select id="findByPage" resultType="DrugBasicMain">
        SELECT
        <include refid="baseSql"/>
        FROM
        comm.dict_drug_basic_main a
        <where>
            hospital_id = #{hospitalId}
            and invalid_flag = '0'
        </where>
    </select>

    <select id="findByDrugCodeSet" resultType="DrugBasicMainPackName">
        SELECT
        <include refid="baseSql"/>,
        dp1.packing_name as minPackName,
        dp2.packing_name as specPackName,
        dc.catagory_name as drugCatagoryName,
        df.dosage_form_name as dosageFormName,
        dsp.drug_spec_packing_id,
        dsp.packing_id as specPackingId,
        dsp.spec as spec,
        dsp.min_dose_count
        FROM
        comm.dict_drug_basic_main a
        left join comm.dict_drug_packing dp1 on dp1.packing_id = a.min_packing_id
        left join comm.dict_drug_spec_packing dsp on  dsp.drug_id = a.drug_basic_main_id
        left join comm.dict_drug_packing dp2 on dp2.packing_id = dsp.packing_id
        left join comm.dict_drug_catagory dc on dc.catagory_id = a.drug_catagory_id
        left join comm.dict_drug_dosage_form df on df.dosage_form_id = a.dosage_form_id
        <where>
            a.drug_code in
            <foreach collection="drugCodeSet" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            and a.invalid_flag = '0'
            and dsp.invalid_flag = '0'
            and a.hospital_id = #{hospitalId}
        </where>

    </select>

    <select id="getDrugMainCount" resultType="int">
        select count(*)
        from comm.dict_drug_basic_main a
        <!--//标准字典去掉hospital_id，his_org_id where hospital_id = #{hospitalId}  and invalid_flag = '0' -->
        where (hospital_id = #{hospitalId}
                   or hospital_id = -1)
        and invalid_flag = '0'
    </select>

    <select id="getDrugData" resultType="DrugBasicMain">
        select
        <include refid="baseSql"/>
        from comm.dict_drug_basic_main a
        <!--//标准字典去掉hospital_id，his_org_id where hospital_id = #{hospitalId}  and invalid_flag = '0' -->
        where (hospital_id = #{hospitalId}
        or hospital_id = -1)
         and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>
    <!--获取药品类别数据-->
    <select id="getDrugEssentialNameList" resultType="java.lang.String">
     select
       catagory_name
     from
      comm.dict_drug_catagory
    where 1=1
    and (catagory_id between 600 and 800 or drug_catagory_type_id=3 )
    and his_org_id = #{hisOrgId}
    and invalid_flag='0'
    </select>

    <select id="getDrugOrMaterialCount" resultType="int">
        select count(*)
        from comm.dict_drug_basic_main a
        where hospital_id = #{hospitalId}
        and invalid_flag = '0'
        and his_creater_name ='交付平台初始导入'
        and material_or_drug = #{materialOrDrug}
    </select>

    <select id="selectListChargePage"
            resultType="com.msun.core.component.implementation.server.drug.entity.po.DrugBasicMain">
        select * from
        comm.dict_drug_basic_main
        where  hospital_id = #{hospitalId}
        and invalid_flag = '0'
        limit #{max_value}  offset  #{limit}
    </select>
</mapper>
