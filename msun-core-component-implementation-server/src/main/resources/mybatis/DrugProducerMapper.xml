<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugProducerMapper">

    <select id="findByProducerNameSet" resultType="DrugProducerPO">

        SELECT
        min(a.producer_id) as producer_id,
        a.producer_cnname
        FROM
        comm.dict_drug_producer a
        <where>
            a.producer_cnname in
            <foreach collection="producerNameSet" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            and a.invalid_flag = '0'
        </where>
        group by   a.producer_cnname
    </select>

    <select id="selectDrugProducerByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugProducer">
        SELECT
            producer_id,
            producer_cnname,
            producer_enname,
            producer_code,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            VERSION,
            address,
            phone,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            hospital_id
        FROM
            comm.dict_drug_producer
        WHERE
            invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )标准字典去掉hospital_id，his_org_id-->

    </select>
</mapper>