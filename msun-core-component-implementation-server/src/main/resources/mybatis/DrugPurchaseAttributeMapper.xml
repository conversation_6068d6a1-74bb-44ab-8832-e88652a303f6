<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugPurchaseAttributeMapper">
   <!--查询采购字典所有数据-->
    <select id="getDrugPurchaseAttributeAll" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugPurchaseAttributePo">
        select
           purchase_attribute_id,
           purchase_attribute_name,
           input_code,
           full_code,
           wb_code,
           his_org_id,
           his_creater_id
        from comm.dict_drug_purchase_attribute
        where invalid_flag='0'
        and his_org_id=#{hisOrgId}
    </select>
    <!--药品字典根据his_org_id为-1和his——org——id为本医院的进行查询-->
    <select id="selectPurchaseAttributeList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugPurchaseAttributePo">
         select
           purchase_attribute_id,
           purchase_attribute_name,
           input_code,
           full_code,
           wb_code,
           his_org_id,
           his_creater_id
        from comm.dict_drug_purchase_attribute
        where invalid_flag='0'
        and (his_org_id= -1 or his_org_id=#{hisOrgId})
    </select>
</mapper>