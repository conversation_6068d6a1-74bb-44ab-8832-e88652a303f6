<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugReimbursementAttributeMapper">
 <!--查询所有的数据-->
    <select id="getAllList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugReimbursementAttributePo">
        select
          reimbursement_attribute_id,
          reimbursement_attribute_name,
          input_code,
          full_code,
          wb_code,
          order_no,
          his_org_id,
          his_creater_id,
          his_creater_name,
          his_create_time,
          his_updater_id,
          his_update_time
        from comm.dict_drug_reimbursement_attribute
        where invalid_flag='0'
        and his_org_id = #{hisOrgId}
    </select>
    <!--查询-1的数据和本医院的数据-->
    <select id="selectReimbursementAttributeList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugReimbursementAttributePo">
       select
          reimbursement_attribute_id,
          reimbursement_attribute_name,
          input_code,
          full_code,
          wb_code,
          order_no,
          his_org_id,
          his_creater_id,
          his_creater_name,
          his_create_time,
          his_updater_id,
          his_update_time
        from comm.dict_drug_reimbursement_attribute
        where invalid_flag='0'
          and
            (his_org_id = -1
            or
            his_org_id = #{hisOrgId})
    </select>
</mapper>