<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugSupplierMapper">


    <select id="findBySupplierNameSet" resultType="DrugSupplierPO">
        SELECT
            min(a.supplier_id) as supplier_id,
            a.supplier_name
        FROM
            comm.dict_drug_supplier a
        <where>
            a.supplier_name in
            <foreach collection="supplierNameSet" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            and a.invalid_flag = '0' AND (a.goods_type ='0' or a.goods_type = #{goodsType})
        </where>
        group by   a.supplier_name
    </select>

    <select id="selectDrugSupplierByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugSupplier">
        SELECT
            supplier_id,
            supplier_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            supplier_code,
            VERSION,
            address,
            phone,
            saleman,
            saleman_phone,
            licence,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            hospital_id
        FROM
            comm.dict_drug_supplier
        WHERE
        invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )标准字典去掉hospital_id，his_org_id-->

    </select>
</mapper>