<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugToxicityMapper">
    <select id="selectDrugToxicityByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugToxicity">
        SELECT
            toxicity_id,
            toxicity_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time
        FROM
            comm.dict_drug_toxicity
        WHERE
            invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )标准字典去掉hospital_id，his_org_id-->
    </select>
</mapper>