<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugUsageMapper">
    <select id="selectDrugUsageByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugUsage">
        SELECT
            usage_id,
            usage_name,
            input_code,
            full_code,
            sort_no,
            invalid_flag,
            other_code,
            in_use_range,
            usage_memo,
            speed_memo_flag,
            infusion_flag,
            speed_memo_mz_flag,
            package_flag,
            transfusion_usages_flag,
            usage_en_flag,
            parent_usage_id,
            VERSION,
            custom_id,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            wb_code
        FROM
            comm.dict_usage
        WHERE
            invalid_flag = '0'
        <!--AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )//标准字典去掉hospital_id，his_org_id-->
    </select>
</mapper>