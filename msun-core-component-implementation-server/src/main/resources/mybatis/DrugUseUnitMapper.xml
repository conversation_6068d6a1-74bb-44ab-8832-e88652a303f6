<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.DrugUseUnitMapper">

    <select id="selectDrugUseUnitByMap"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DrugUseUnit">
        SELECT
            use_unit_id,
            use_unit_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            VERSION,
            his_org_id

        FROM
            comm.dict_drug_use_unit
        WHERE
            invalid_flag = '0'
          <!-- AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )-->

    </select>
</mapper>