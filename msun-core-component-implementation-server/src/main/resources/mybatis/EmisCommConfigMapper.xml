<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.EmisCommConfigMapper">
    <update id="updateEmisCommConfig" parameterType="com.msun.core.component.implementation.server.imsp.entity.po.EmisCommConfig">
        update comm.config
        set config_value = #{configValue},
        his_update_time =#{hisUpdateTime},
        his_updater_id = #{hisUpdaterId}
        where config_code = #{configCode}
    </update>
</mapper>