<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.fixchargeconfig.dao.FixChargeConfigMapper">
    <sql id="baseSql">
        fix_charge_config_id,
        hospital_id,
        hospital_name,
        ward_id,
        ward_name,
        charge_id,
        price,
        charge_name,
        quantity,
        begin_charge_date,
        last_charge_date,
        invalid_flag,
        begin_charge_flag
    </sql>

    <!--  根据费用名称分页查询  -->
    <select id="findByPage" resultType="com.msun.core.component.implementation.server.fixchargeconfig.entity.po.FixChargeConfig">
        select
            <include refid="baseSql"/>
        from
            fix_charge_config
        <where>
            <if test="chargeName != null and chargeName != ''">
                charge_name like CONCAT('%',#{chargeName},'%')
            </if>
            and his_org_id = #{orgId}
        </where>
    </select>


</mapper>
