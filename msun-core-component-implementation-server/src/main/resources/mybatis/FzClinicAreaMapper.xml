<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.FzClinicAreaMapper">
    <!--查询诊区数据-->
    <select id="getFzClinicAreaList" resultType="com.msun.core.component.implementation.api.imsp.vo.FzClinicAreaVo">
          SELECT
           clinic_area_id,
           clinic_area_name,
           clinic_area_alias_name,
           input_code,
           full_code,
           hospital_id,
           memo,
           his_org_id,
           his_create_time,
           his_creater_id,
           his_creater_name,
           his_update_time,
           his_updater_id,
           version,
           his_updater_name,
           enable_flag,
           allow_online_flag,
           hospital_name,
           allow_call_flag,
           is_auto_triage,
           area_signal_model,
           area_whether_pay,
           is_promptly_triage,
           today_smc_triage,
           area_patient_time_triage
        FROM tigc.fz_clinic_area
        where hospital_id = #{hospitalId}
           and his_org_id = #{hisOrgId}
           and delete_flag='0'
    </select>
</mapper>