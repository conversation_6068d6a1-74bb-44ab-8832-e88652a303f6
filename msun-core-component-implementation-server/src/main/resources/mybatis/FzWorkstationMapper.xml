<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.FzWorkstationMapper">
<!--查询工作站表-->
    <select id="getFzWorkstationList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.FzWorkstationVo">
    SELECT
         workstation_id,
         workstation_name,
         scene_type,
         input_code,
         full_code,
         delete_flag,
         room_id,
         workstation_mac,
         clinic_doc_id,
         clinic_dept_id,
         clinic_time,
         hospital_id,
         memo,
         sort_no,
         his_org_id,
         his_create_time,
         his_creater_id,
         his_creater_name,
         his_update_time,
         his_updater_id,
         version,
         his_updater_name,
         scene_subtype,
         scene_subtype_name,
         scene_type_name,
         workstation_machine_code,
         hospital_name,
         first_clinic_time
     FROM tigc.fz_workstation
     where hospital_id = #{hospitalId}
           and his_org_id = #{hisOrgId}
           and delete_flag='0'
    </select>
</mapper>