<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.publichealth.dao.HdPersonalBaseMapper">


    <select id="getHdPersonalBaseCount" resultType="int">
        select count(*) from phpg_nbphs.hd_personal_base where  hospital_id = #{hospitalId}
    </select>

    <select id="getFamilyArchivesCount" resultType="int">
        SELECT count(*) FROM PHPG_NBPHS.HD_FAMILY_ARCHIVES HFA WHERE HFA.ID IN ( SELECT DISTINCT FAMILY_ID FROM PHPG_NBPHS.HD_FAMILY_MEMBER HFM LEFT JOIN PHPG_NBPHS.HD_PERSONAL_BASE HPB ON HFM.PERSONAL_BASE_ID = HPB.ID AND HFM.RELATIONSHIP = '0' WHERE HPB.hospital_id= #{hospitalId} )
    </select>

    <select id="getHdPersonalBaseDiseaseCount" resultType="int">
        select count(*) FROM phpg_nbphs.hd_personal_base_disease hpbo LEFT JOIN phpg_nbphs.hd_personal_base hpb ON hpb.id = hpbo.personal_base_id where hpb.hospital_id=  #{hospitalId}
    </select>
    <select id="getHdPersonalBaseHomeHisCount" resultType="int">
        select count(*) FROM phpg_nbphs.hd_personal_base_home_his hpbo LEFT JOIN phpg_nbphs.hd_personal_base hpb ON hpb.id = hpbo.personal_base_id where hpb.hospital_id=  #{hospitalId}
    </select>
    <select id="getHdPersonalBaseObcCount" resultType="int">
        select count(*)  FROM phpg_nbphs.hd_personal_base_obc hpbo LEFT JOIN phpg_nbphs.hd_personal_base hpb ON hpb.id = hpbo.personal_base_id where hpb.hospital_id=  #{hospitalId}
    </select>
    <select id="getFamilyMemberCount" resultType="int">
        select count(*)  FROM PHPG_NBPHS.HD_FAMILY_MEMBER WHERE FAMILY_ID IN ( SELECT DISTINCT FAMILY_ID FROM PHPG_NBPHS.HD_FAMILY_MEMBER HFM LEFT JOIN PHPG_NBPHS.HD_PERSONAL_BASE HPB ON HFM.PERSONAL_BASE_ID = HPB.ID AND HFM.RELATIONSHIP = '0' WHERE HPB.hospital_id= #{hospitalId} )
    </select>

    <select id="getFaCardsCount" resultType="int">
        SELECT count(*) FROM equipment.fa_cards WHERE hospital_id = #{hospitalId} and invalid_flag = 0
    </select>

    <select id="getChisappDataCount" resultType="int">
        ${sql}
    </select>
    <select id="getChisappStringData" resultType="string">
        ${sql}
    </select>
    <select id="getChisappDataOfListResult" resultType="map">
        ${sql}
    </select>

    <select id="getCssdMpTypeCount" resultType="int">
        SELECT count(*) FROM cssd.medical_package_type WHERE hospital_id = #{hospitalId} and invalid_flag = 0
    </select>

</mapper>
