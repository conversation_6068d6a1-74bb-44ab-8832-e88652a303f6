<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.HospitalMapper">
    <select id="getOneData" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Hospital">
        select *
        from comm.hospital h
        where h.hospital_id = #{hospitalId} and h.his_org_id = #{orgId}
    </select>

    <!-- 根据数据库名称查询所有schema -->
    <select id="getAllSchemaByDatabase" resultType="java.lang.String">
        SELECT schema_name FROM information_schema.schemata WHERE catalog_name = #{tableSource}
    </select>
    <!-- 根据schema查询所有表名 -->
    <select id="getAllTableNameBySchema" resultType="java.lang.String">
        SELECT table_name FROM information_schema.tables WHERE table_catalog = #{tableSource} AND table_schema = #{tableSchema}
    </select>
    <!-- 根据表名查询所有权限 -->
    <select id="getAllGrantsByTableName" resultType="java.lang.String">
        select distinct privilege_type from information_schema.role_table_grants where table_catalog = #{tableSource} AND table_schema = #{tableSchema} AND table_name = #{tableName}
    </select>
    <!--查询医院数据-->
    <select id="getHospitalList" resultType="com.msun.core.component.implementation.api.imsp.vo.HospitalVO">
     select *
        from comm.hospital h
        where h.hospital_id = #{hospitalId} and h.his_org_id = #{hisOrgId}
    </select>
</mapper>