<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.hqc.dao.HqcDataMapper">


    <!--查询所有新版质控规则-->
    <select id="getAllRules" resultType="java.util.Map">
        select  rule_name,
                rule_desc,
                rule_code,
                rule_type::varchar,
                case when rule_type=1 then  '时限类'
                     when rule_type=2 then  '缺陷类'
                     when rule_type=3 then  '一致类'
                     when rule_type=5 then  '内涵类' else  '其他' end rule_type_name
        from "hqc"."qc_emr_rule"
        where rule_type != 5
        order by rule_code
    </select>

    <!--批量更新开启、关闭-->
    <update id="updateEmrRuleBatch" parameterType="com.msun.core.component.implementation.api.hqc.dto.HqcEmrRuleDTO">
        <foreach collection="emrRuleList" item="item" separator=";">
            update "hqc"."qc_emr_rule" set is_enabled = #{item.isEnabled},hospital_id = #{item.hospitalId},his_org_id=#{item.hisOrgId},his_update_time=current_timestamp where rule_code = #{item.ruleCode}
        </foreach>
    </update>
</mapper>