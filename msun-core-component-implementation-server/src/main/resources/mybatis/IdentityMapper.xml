<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.IdentityMapper">
    <select id="getIdentity" resultType="java.util.Map">
        select i.identity_id "identityId",u.user_name "userName", u.user_code "userCode"
        from comm.identity i
        left join comm.user u on i.user_id = u.user_id
        where i.delete_flag = '0' and u.user_name is not null
          and i.hospital_id = #{hospitalId} and i.his_org_id = #{hisOrgId}
    </select>

    <select id="getIdentityCount" resultType="integer">
        select count(1)
        from comm.identity i
        left join comm.user u on i.user_id = u.user_id
        where i.delete_flag = '0' and u.user_name is not null
          and i.hospital_id = #{hospitalId} and i.his_org_id = #{hisOrgId}
    </select>
</mapper>
