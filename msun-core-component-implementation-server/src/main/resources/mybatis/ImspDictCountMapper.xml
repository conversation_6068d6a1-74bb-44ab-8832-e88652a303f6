<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ImspDictCountMapper">
    <!--14_3、挂号类别  拉取生产库字典 查询-->
    <select id="getInventoryPriceCountFour"
            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">
     select a.yc_dept_id as "deptId", b.dept_name as "deptName",sum(a.stock_amount) as "stockAmount",sum(a.retail_price*stock_amount) as "retailPrice",sum(buy_price*stock_amount) as "buyPrice"
     from  res.yc_stock_query a
     left join comm.dept b on a.yc_dept_id =b.dept_id
     where stock_query_type='1'  and b.category_id in ('4')
     group by a.yc_dept_id,b.dept_name;
    </select>

    <!--14_4、发票项目  拉取生产库字典 查询-->
    <select id="getInventoryPriceCountNotFour"
            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">
         select a.yc_dept_id as "deptId", b.dept_name as "deptName",sum(a.stock_amount) as "stockAmount",sum(a.retail_price*stock_amount)  as "retailPrice",sum(buy_price*stock_amount) as "buyPrice" from  res.yc_stock_query a
       left join comm.dept b on a.yc_dept_id =b.dept_id
       where stock_query_type='1'  and b.category_id not in ('4')
       group by a.yc_dept_id,b.dept_name;
    </select>
<!--    getInventoryPriceCountFour  getInventoryPriceCountNotFour  getInventoryPriceCountFive  getInventoryPriceCountTwelve  getPatientCount  getInPatientCount-->
    <!--14_1、生产厂商  拉取生产库字典 查询-->
    <select id="getInventoryPriceCountFive"
            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">
    select a.yc_dept_id as "deptId", b.dept_name as "deptName",sum(a.stock_amount) as "stockAmount",sum(a.retail_price*stock_amount)  as "retailPrice",sum(buy_price*stock_amount) as "buyPrice" from  res.yc_stock_query a
left join comm.dept b on a.yc_dept_id =b.dept_id

where stock_query_type='0'  and b.category_id in ('5','12')
group by a.yc_dept_id,b.dept_name;
    </select>

    <select id="getInventoryPriceCountTwelve"
            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">
select a.yc_dept_id as "deptId", b.dept_name as "deptName",sum(a.stock_amount) as "stockAmount",sum(a.retail_price*stock_amount)  as "retailPrice",sum(buy_price*stock_amount) as "buyPrice" from  res.yc_stock_query a
left join comm.dept b on a.yc_dept_id =b.dept_id

where stock_query_type='0'  and b.category_id in ('12')
group by a.yc_dept_id,b.dept_name;
    </select>
<!--    <select id="getPatientCount"-->
<!--            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">-->
<!--select sum(amount) from finance.in_prepay_record where pat_in_hos_id in (select pih.pat_in_hos_id from zy.pat_in_hospital pih inner join zy.pat_in_hospital_status pihs on pihs.pat_in_hos_id = pih.pat_in_hos_id where pihs.pat_in_status = '1') and pay_status = '1' -->
<!--    </select>-->
    <select id="getInPatientCount"
            resultType="com.msun.core.component.implementation.api.imsp.vo.InventoryQuantityAmountVo">
        select a.pat_in_hos_id,a.total_cost AS "amount",b.pat_name
        from finance.in_pat_charge_property a
                 left join zy.pat_in_hospital b on a.pat_in_hos_id = b.pat_in_hos_id
                 left join zy.pat_in_hospital_status C ON C.pat_in_hos_id = b.pat_in_hos_id
        where C.pat_in_status = 1
    </select>

    <select id="getInPatientCountInteger"
            resultType="Integer">
         select count(1) from finance.in_bill_record ibr
         left join zy.pat_in_hospital pih on pih.pat_in_hos_id=ibr.pat_in_hos_id
         inner join zy.pat_in_hospital_status pihs on pihs.pat_in_hos_id = pih.pat_in_hos_id where pihs.pat_in_status = '1' group by pih.pat_name
    </select>

<!--    -->
    <select id="getPatient"
            resultType="Integer">
         select count(1) from comm.patient where invalid_flag = '0';
    </select>

    <select id="getPatAccount"
            resultType="BigDecimal">
         select sum(balance) from finance.pat_account
    </select>
    <select id="getPatInHospital"
            resultType="Integer">
            select count(1) from zy.pat_in_hospital pih inner join zy.pat_in_hospital_status pihs on pihs.pat_in_hos_id = pih.pat_in_hos_id
    </select>
    <select id="getInPrepayRecord"
            resultType="BigDecimal">
    select sum(amount) from finance.in_prepay_record where pat_in_hos_id in (select pih.pat_in_hos_id from zy.pat_in_hospital pih inner join zy.pat_in_hospital_status pihs on pihs.pat_in_hos_id = pih.pat_in_hos_id where pihs.pat_in_status = '1') and pay_status = '1'    </select>
    <select id="getInBillRecord"
            resultType="Integer">
    select count(1) from finance.in_bill_record where  pat_in_hos_id in (select pih.pat_in_hos_id from zy.pat_in_hospital pih inner join zy.pat_in_hospital_status pihs on pihs.pat_in_hos_id = pih.pat_in_hos_id where pihs.pat_in_status = '1')    </select>

</mapper>