<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ImpsMapper">


    <select id="getHospitalId" resultType="com.msun.core.component.implementation.api.imsp.vo.HospitalVO">
             select
            h.hospital_id as hospitalId,
           h.hospital_name as hospitalName,
           h.his_org_id as hisOrgId,
           dhc.domain_url  as domain
        from
            hospital h
        left join domain_hospital_config dhc on
            h.hospital_id = dhc.hospital_id
        where
            h.hospital_name = #{hospticalName}

    </select>
</mapper>
