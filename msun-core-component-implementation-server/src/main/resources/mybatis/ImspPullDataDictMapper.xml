<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ImspPullDataDictMapper">

    <!--查询包装单位数据-->
    <select id="getDrugBasicMainList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DrugBasicMainDictVO">
        SELECT
          *
        FROM
         comm.dict_drug_basic_main drug
        WHERE
        invalid_flag = '0'
          AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}

         and material_or_drug=#{materialOrDrug}
ORDER BY
        drug.drug_basic_main_id
        DESC
        LIMIT
        #{pageSize}
        OFFSET
        #{startLine}
    </select>


    <!--    &lt;!&ndash;查询包装单位数据&ndash;&gt;-->
    <!--    <select id="getMaterialMainList" resultType="com.msun.core.component.implementation.api.imsp.vo.MaterialMainDictPOVO">-->
    <!--        SELECT-->
    <!--         *-->
    <!--        FROM-->
    <!--         comm.dict_material_main-->
    <!--        WHERE-->
    <!--        invalid_flag = '0'-->
    <!--        AND  his_org_id = #{hisOrgId}-->
    <!--        AND  hospital_id = #{hospitalId}-->
    <!--        ORDER BY-->
    <!--        drug.drug_basic_main_id-->
    <!--        DESC-->
    <!--        LIMIT-->
    <!--        #{pageSize}-->
    <!--        OFFSET-->
    <!--        #{startLine}-->
    <!--    </select>-->
    <!--查询包装单位数据-->
    <select id="getPatientList" resultType="com.msun.core.component.implementation.api.imsp.vo.PatientVO">
        SELECT
         *
        FROM
         comm.patient pat
        WHERE
        invalid_flag = '0'
         AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}

        ORDER BY
       pat.pat_id
        DESC
        LIMIT
        #{pageSize}
        OFFSET
        #{startLine}
    </select>

    <!--查询包装单位数据-->
    <select id="getWardList" resultType="com.msun.core.component.implementation.api.imsp.vo.WardVO">
        SELECT
        *
        FROM
         comm.ward
        WHERE
        invalid_flag = '0'
         AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}
    </select>
    <!--查询包装单位数据-->
    <select id="getDiagnosisClinicList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDiagnosisClinicVo">
        SELECT
         *
        FROM
         comm.dict_diagnosis_clinic cli
        WHERE
        invalid_flag = '0'
         AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}
          ORDER BY
       cli.dict_diagnosis_clinic_id
        DESC
        LIMIT
        #{pageSize}
        OFFSET
        #{startLine}
    </select>
    <!--查询包装单位数据-->
    <select id="getInvoiceItemTypeList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictInvoiceItemVo">
             select invoice_item_id    "invoiceItemId", --发票项目id
      invoice_item_code     "invoiceItemCode",--发票项目编码
      invoice_item_name     "invoiceItemName",--发票项目名称
      item.dict_item_type_id "dictItemTypeId",--发票项目分类id
      dict.dict_item_type_code "dictItemTypeCode",--发票项目分类编码
      dict.dict_item_type_name "dictItemTypeName"--发票项目分类名称
      from comm.dict_invoice_item item
      left join comm.dict_item_type dict on item.dict_item_type_id = dict.dict_item_type_id
      where item.invalid_flag = '0'
             AND (item.his_org_id = - 1 OR item.his_org_id = #{hisOrgId})
      order by "invoiceItemName"

    </select>

    <!--查询包装单位数据-->
    <select id="getClinicCount" resultType="int">
       SELECT
        count(1)
        FROM
         comm.dict_diagnosis_clinic
        WHERE
        invalid_flag = '0'
         AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}
    </select>
    <!--查询包装单位数据-->
    <select id="getPatientCount" resultType="int">
         SELECT
          count(1)
        FROM
         comm.patient
        WHERE
        invalid_flag = '0'
         AND  his_org_id = #{hisOrgId}
        AND  hospital_id = #{hospitalId}
    </select>
    <!--查询剂量单位{最小剂量/单位剂量表-->
    <select id="getDictDrugDoseUnitList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugDoseUnitVo">
     SELECT
        dose_unit_id,
        dose_unit_name,
        input_code,
        full_code,
        wb_code
     FROM comm.dict_drug_dose_unit
     WHERE invalid_flag = '0'
     AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})


    </select>
    <!--查询费用类型-->
    <select id="getDictCostCatagoryChargeList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictOrderCategoryVO">
      SELECT
       order_category_id,
       order_category_name,
       input_code,
       full_code,
       wb_code
      FROM comm.dict_order_category
      WHERE 1=1
     AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <!--用途分类-->
    <select id="getDictDrugUseUnitList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugUseUnitVO">
      SELECT
        use_unit_id,
        use_unit_name,
        input_code,
        full_code,
        wb_code
      FROM comm.dict_drug_use_unit
      WHERE invalid_flag = '0'
      AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <!--频次类别-->
    <select id="getDictFrequencyList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictFrequencyVO">
    SELECT
    frequency_id,
     frequency_name,
     input_code,
     other_code,
      full_code,
      invalid_flag,
      count_everytime,
      memo,
      flag_show,
      exec_days,
      frequency_type,
      order_no,
       in_use_range,
        infusion_time,
         default_exec_time_id,
         infusion_priority,
         infusion_interval,
         flag_frequency_en,
         in_flag,
         out_flag,
         wb_code,
         out_drug_flag
     FROM comm.dict_frequency
     WHERE invalid_flag='0'
      AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <select id="getDictDrugEssentialList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DrugEssentialVO">
            SELECT
            essential_drug_id,
            essential_drug_name,
            input_code,
            full_code
           FROM comm.dict_drug_essential
           WHERE invalid_flag='0'
           AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <!--抗菌药物分类-->
    <select id="getDictDrugAntibioticsTypeList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DrugAntibioticsTypeVO">
      SELECT
       antibiotics_type_id,
       antibiotics_type_name,
       input_code,
       full_code,
       wb_code
      FROM comm.dict_drug_antibiotics_type
      WHERE invalid_flag='0'
      AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <!--抗菌药物等级-->
    <select id="getDictDrugAntibioticsLevelList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DrugAntibioticsLevelVO">
     SELECT
        antibiotics_level_name,
        input_code,
        full_code,
        wb_code
      FROM comm.dict_drug_antibiotics_level
      WHERE invalid_flag='0'
      AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <!--用药途径-->
    <select id="getDictUsageList" resultType="com.msun.core.component.implementation.api.imsp.vo.DictUsageVO">
     SELECT usage_id,
            usage_name,
            input_code,
            full_code
      FROM comm.dict_usage
      WHERE invalid_flag='0'
      AND (his_org_id = - 1 OR his_org_id = #{hisOrgId})
    </select>
    <select id="getAuthorizationAllInfo"
            resultType="com.msun.core.component.implementation.api.imsp.vo.SelectAuthorizationVO">
        SELECT
            aa.published_system_code "publishedSystemCode",
            aa.published_system_standard_name "publishedSystemStandardName",
            aa.published_system_common_name "publishedSystemCommonName",
            aa.published_system_menu_code "publishedSystemMenuCode",
            bb.hospital_id,
            min(bb.his_create_time) as "createTime",
            count(bb.secret_key) as allcount
        FROM
            comm.published_system aa
        LEFT JOIN comm.system_authorize_info bb on aa.published_system_id = bb.published_system_id and bb.invalid_flag = '0'
        group by   aa.published_system_code,
                   aa.published_system_standard_name,
                   aa.published_system_common_name,
                   aa.published_system_menu_code,
                   bb.hospital_id
        order by aa.published_system_code
    </select>
</mapper>