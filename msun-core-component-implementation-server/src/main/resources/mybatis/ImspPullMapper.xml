<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ImspPullMapper">
    <!--14_3、挂号类别  拉取生产库字典 查询-->
    <select id="getRegisterClassList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictRegisterClassVo">
        SELECT
          register_class_id,
          register_class_name,
          input_code,
          full_code,
          order_no,
          invalid_flag,
          free_flag,
          default_recipe_id,
          wb_code,
          his_org_id,
          his_creater_id,
          his_creater_name,
          his_create_time,
          his_updater_id,
          his_update_time,
          version,
          hospital_id
        FROM
          comm.dict_register_class
        where (his_org_id = -1 or his_org_id = #{hisOrgId})
        and hospital_id = #{hospitalId}
         and invalid_flag = '0'
    </select>

    <!--14_4、发票项目  拉取生产库字典 查询-->
    <select id="getInvoiceItemList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictInvoiceItemVo">
            SELECT
                invoice_item_id,
                invoice_item_code,
                invoice_item_name,
                input_code,
                full_code,
                wb_code,
                invalid_flag,
                order_no,
                his_org_id,
                his_creater_id,
                his_creater_name,
                his_create_time,
                his_updater_id,
                his_update_time,
            VERSION
            FROM
                comm.dict_invoice_item
             WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
             and invalid_flag = '0'
    </select>

    <!--14_1、生产厂商  拉取生产库字典 查询-->
    <select id="getDrugProducerList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugProducerVo">
                SELECT
                    producer_id,
                    producer_cnname,
                    producer_enname,
                    producer_code,
                    input_code,
                    full_code,
                    wb_code,
                    order_no,
                    invalid_flag,
                    VERSION,
                    address,
                    phone,
                    his_org_id,
                    his_creater_id,
                    his_creater_name,
                    his_create_time,
                    his_updater_id,
                    his_update_time,
                    hospital_id
                FROM
                    comm.dict_drug_producer
                WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
             and invalid_flag = '0'
    </select>
   <!--14_2、供应厂商  拉取生产库字典 查询-->
    <select id="getDrugSupplierList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugSupplierVo">
            SELECT
                supplier_id,
                supplier_name,
                input_code,
                full_code,
                wb_code,
                order_no,
                invalid_flag,
                supplier_code,
                VERSION,
                address,
                phone,
                saleman,
                saleman_phone,
                licence,
                his_org_id,
                his_creater_id,
                his_creater_name,
                his_create_time,
                his_updater_id,
                his_update_time,
                hospital_id
            FROM
                comm.dict_drug_supplier
           WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
             and invalid_flag = '0'
    </select>

    <!--14_5、核算项目  拉取生产库字典 查询-->
    <select id="getAccountItemList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictAccountItemVo">
        SELECT
            account_item_id,
            account_item_code,
            account_item_name,
            input_code,
            full_code,
            wb_code,
            invalid_flag,
            order_no,
            group_no1,
            group_no2,
            group_no3,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
        VERSION
        FROM
            comm.dict_account_item
          WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
             and invalid_flag = '0'
    </select>

    <!--14_6、病案项目 拉取生产库字典 查询-->
    <select id="getMedRecItemList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictMedRecItemVo">
        SELECT
            med_rec_item_id,
            med_rec_item_code,
            med_rec_item_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
        VERSION
        FROM
            comm.dict_med_rec_item
        WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
             and invalid_flag = '0'
    </select>

    <!--14_7、药品类别 拉取生产库字典 查询-->
    <select id="getDrugOrderTypeList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DrugOrderTypeVo">
        SELECT
            drug_order_type_id,
            drug_order_type_name,
            categroy_ids,
            begin_categroy_id,
            end_categroy_id,
            pres_type,
            order_no,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time
        FROM
            comm.drug_order_type
       WHERE
               ( his_org_id = -1
             or his_org_id = #{hisOrgId} )
    </select>

    <!--      * 14_8、药品分类表(DICT_DRUG_CATAGORY) 拉取生产库字典 查询 -->
    <select id="getDrugCatagoryList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictDrugCatagoryVO">
        SELECT
            catagory_id,
            catagory_name,
            input_code,
            full_code,
            wb_code,
            order_no,
            invalid_flag,
            catagory_code,
            parent_id,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            retail_price_ratio,
            drug_flag,
            material_flag
        FROM
             comm.dict_drug_catagory
        WHERE
            invalid_flag = '0'
            AND  ( his_org_id = -1
             or his_org_id = #{hisOrgId} )


    </select>

    <!--14_9 科室数据-->
    <select id="getDeptList" resultType="com.msun.core.component.implementation.api.imsp.vo.DeptVO">
        SELECT
            dept_id,
            hospital_id,
            dept_code,
            dept_name,
            alias_name,
            input_code,
            input_alias_code,
            full_code,
            full_alias_code,
            invalid_flag,
            start_time,
            end_time,
            category_id,
            sort_order,
            parent_id,
            register_flag,
            VERSION,
            wb_code,
            LEVEL,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            account_dept_id,
            room_address,
            msun_org_id,
            national_dept_insurance_code,
            hospital_area_id
        FROM
            comm.dept
        where  invalid_flag = '0'
            AND  ( his_org_id = -1 or his_org_id = #{hisOrgId} )
            and hospital_id = #{hospitalId}
    </select>

    <!-- 公卫对照云健康机构 -->
    <select id="getOrgOrDeptList" resultType="com.msun.core.component.implementation.api.imsp.vo.OrgDeptVO">
         SELECT
             h.hospital_id as org_or_dept_id,
             h.hospital_code as org_or_dept_code,
             h.hospital_name as org_or_dept_name,
             h.alias_name as org_alias_name,
             2 org_or_dept_level,
             hp.hospital_id as pid,
             hp.hospital_code as pcode,
             hp.hospital_name as pname,
             hp.alias_name as palias_name
        FROM
					comm.hospital h
						left join  comm.hospital hp on hp.hospital_id = h.parent_id
        where  h.his_org_id = #{hisOrgId}
				  AND  h.hospital_id not in (

						  SELECT
								parent_id
							FROM
								comm.hospital
							where   his_org_id = #{hisOrgId}
							and parent_id is not null

						)

         union all
			  SELECT
             hospital_id as org_or_dept_id,
             hospital_code as org_or_dept_code,
             hospital_name as org_or_dept_name,
             alias_name as org_alias_name,
             1 org_or_dept_level,
						  0 as pid,
             '' as pcode,
             '' as pname,
             '' as palias_name
        FROM
            comm.hospital
        where   his_org_id = #{hisOrgId}
            AND  hospital_id in (

						  SELECT
								parent_id
						FROM
								comm.hospital
						where   his_org_id = #{hisOrgId}
						and parent_id is not null

						)

         union all
        SELECT
         d.dept_id as org_or_dept_id,
         d.dept_code as org_or_dept_code,
         d.dept_name as org_or_dept_code,
        d.alias_name as org_alias_name,
         3 org_or_dept_level,
					  h.hospital_id as pid,
             h.hospital_code as pcode,
             h.hospital_name as pname,
             h.alias_name as palias_name
        FROM
            comm.dept d
				inner join  comm.hospital h on d.hospital_id = h.hospital_id
        where   d.his_org_id = #{hisOrgId}

    </select>

    <!--  公卫对照云健康用户 -->
    <select id="getUserList" resultType="com.msun.core.component.implementation.api.imsp.vo.UserGongWeiVO">
        SELECT
            user_id,
            user_code,
            user_name,
            his_org_id,
	        hospital_id
        FROM
        comm.user
        where   his_org_id = #{hisOrgId}
    </select>

    <select id="getFaDeviceCardsCount" resultType="int">
        SELECT count(*) FROM device.fa_device_cards WHERE hospital_id=#{hospitalId} and invalid_flag = 0
    </select>


    <select id="getChisDataCount" resultType="int">
        ${sql}
    </select>
    <select id="getChisStringData" resultType="string">
        ${sql}
    </select>
    <select id="getChisDataOfListResult" resultType="map">
        ${sql}
    </select>
</mapper>