<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.mchs.dao.MchsMapper">
    <update id="batchSysConfigSave">
        update chis.comm.config
        set config_value= #{configValue},
            his_update_time = now()
        where config_code=#{configCode} and   his_org_id= #{hisOrgId}
    </update>


    <select id="selectallDept" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
        select dept_id as deptId,dept_name as deptName from  comm.dept
        <where>
            hospital_id = #{hospitalId}
            and invalid_flag = '0'
        </where>
    </select>

</mapper>