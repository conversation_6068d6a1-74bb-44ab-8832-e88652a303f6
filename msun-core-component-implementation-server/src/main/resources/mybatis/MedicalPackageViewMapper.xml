<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.cssd.dao.MedicalPackageViewMapper">


    <insert id="insertMat" parameterType="com.msun.core.component.implementation.server.cssd.entity.po.MedicalApparatusPo">

    </insert>

    <insert id="insertMas" parameterType="com.msun.core.component.implementation.server.cssd.entity.po.MedicalApparatusSpecPo">

    </insert>

    <insert id="insertMptMas" parameterType="com.msun.core.component.implementation.server.cssd.entity.po.MedicalPackageConfigPo">

    </insert>

    <insert id="insertMpt" parameterType="com.msun.core.component.implementation.server.cssd.entity.po.MedicalPackageTypePo">

    </insert>

    <select id="getDeptListByDeptNameSet" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
        select
        a.dept_id,
        a.dept_code,
        a.category_id,
        a.dept_name
        from comm.dept a
        <where>
            <if test="deptNameSet != null and deptNameSet.size() != 0">
             a.dept_name in
                <foreach collection="deptNameSet" index="index" item="item"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deptId != null">
                and a.dept_id = #{deptId}
            </if>
            and a.invalid_flag = '0'
            and a.hospital_id = #{hospitalId}
        </where>
    </select>

    <select id="aimsGetDeptListByDeptNameSet" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Dept">
        select
        a.dept_id,
        a.dept_code,
        a.category_id,
        a.dept_name
        from comm.dept a
        <where>
            <if test="deptNameSet != null and deptNameSet.size() != 0">
                a.dept_name in
                <foreach collection="deptNameSet" index="index" item="item"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and a.invalid_flag = '0'
            and a.hospital_id = #{hospitalId}
        </where>
    </select>

    <select id="getUserBydeptId" resultType="com.msun.core.component.implementation.server.cssd.entity.po.CssdSysHisUserPo">
        select
            u.user_id hisUserId,
            u.user_name hisUserName,
            u.dept_id hisDeptId,
            u.dept_name hisDeptName,
            u.user_id hisUserSysId,
            '' hisUserRole,
            0 invalidFlag,
            u.hospital_id hospitalId
        from comm."user" u
        where u.dept_id=#{deptId} and hospital_id=#{hospitalId} and invalid_flag = '0'
    </select>

    <update id="cssdUpdateConfig">
        update cssd.material_config set value=#{configValue} ,his_update_time = now() where key=#{configCode}
    </update>



</mapper>
