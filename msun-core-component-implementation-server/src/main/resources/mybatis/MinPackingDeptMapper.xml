<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.DictDrugMinPackingDeptMapper">

    <sql id="baseSql">
        min_packing_dept_id,
        drug_spec_packing_id,
        dept_id,
        drug_id,
        version,
        invalid_flag,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        hospital_id
    </sql>



    <select id="findBySpecPackingIdAndDeptId"  resultType="dictDrugMinPackingDept">
        select
             <include refid="baseSql"/>
        from
            comm.dict_drug_min_packing_dept
        <where>
            <if test="deptId != null">
                 dept_id = #{deptId}
            </if>
            <if test=" drugSpecPackingIdList != null and drugSpecPackingIdList.size() >0">
                and drug_spec_packing_id in
                <foreach collection="drugSpecPackingIdList" index="index" item="item"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>






</mapper>

