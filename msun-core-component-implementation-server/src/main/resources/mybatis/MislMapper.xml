<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.misl.dao.MislMapper">
    <select id="checkDiag" resultType="java.lang.Integer">
        select count(1) from comm.dict_diagnosis_clinic where invalid_flag='0' and national_insurance_code !=''
        <if test="hospitalId != null">
            and hospital_id in(#{hospitalId},-1)
        </if>
    </select>

    <select id="checkOper" resultType="java.lang.Integer">
        select count(1) from comm.dict_surgeries  where invalid_flag = '0' and national_insurance_code !=''
        <if test="hospitalId != null">
            and hospital_id in(#{hospitalId},-1)
        </if>
    </select>

    <select id="checkDept" resultType="java.lang.Integer">
        select count(1) from comm.dept where invalid_flag = '0' and national_dept_insurance_code !=''
        <if test="hospitalId != null">
            and hospital_id = #{hospitalId}
        </if>
    </select>

    <select id="checkUser" resultType="java.lang.Integer">
        select count(1) from comm.user where invalid_flag = '0' and medical_insurance_code !=''
        <if test="hospitalId != null">
            and hospital_id = #{hospitalId}
        </if>
    </select>

    <select id="checkDays" resultType="java.lang.Integer">
        select count(1) from comm.config  where config_code ='upload_out_time_day' and config_value != ''
    </select>

    <select id="checkQualityUrl" resultType="java.lang.Integer">
        select count(1) from comm.config  where config_code = 'qua_service_url' and config_value !=''
    </select>
</mapper>
