<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.material.dao.MmsChargeMapper">

    <select id="selectDictInvoiceItem"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictInvoiceItemVo">
        select invoice_item_id   as invoiceItemId,
        invoice_item_code as invoiceItemCode,
        invoice_item_name as invoiceItemName,
        input_code   as inputCode,
        full_code    as fullCode,
        invalid_flag as invalidFlag
        from comm.dict_invoice_item
        <where> invalid_flag = '0'
            <if test="invoiceItemName != null and invoiceItemName != ''">
                AND ( (invoice_item_name ILIKE CONCAT('%', #{invoiceItemName}, '%'))
                or (input_code ILIKE CONCAT('%', #{invoiceItemName}, '%'))
                or (full_code ILIKE CONCAT('%', #{invoiceItemName}, '%')))
            </if>
        </where>
        limit 100
    </select>


    <select id="selectDictMedRecItem"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictMedRecItemVo">
        select
        med_rec_item_id as medRecItemId,
        med_rec_item_code as medRecItemCode,
        med_rec_item_name as medRecItemName,
        input_code as inputCode,
        full_code as fullCode,
        invalid_flag as invalidFlag
        from
        comm.dict_med_rec_item
        <where> invalid_flag = '0'
            <if test="medRecItemName != null and medRecItemName != ''">
                AND ( (med_rec_item_name ILIKE CONCAT('%', #{medRecItemName}, '%'))
                or (input_code ILIKE CONCAT('%', #{medRecItemName}, '%'))
                or (full_code ILIKE CONCAT('%', #{medRecItemName}, '%')))
            </if>
        </where>
        limit 100
    </select>


    <select id="selectAccountList"
            resultType="com.msun.core.component.implementation.api.imsp.vo.DictAccountItemVo">
        select
            account_item_id as accountItemId,
            account_item_code as accountItemCode,
            account_item_name as accountItemName,
            input_code as inputCode,
            wb_code as wbCode
        from
            comm.dict_account_item
        <where> invalid_flag = '0'
            <if test="accountItemName != null and accountItemName != ''">
                AND ( (account_item_name ILIKE CONCAT('%', #{accountItemName}, '%'))
                or (input_code ILIKE CONCAT('%', #{accountItemName}, '%'))
                or (full_code ILIKE CONCAT('%', #{accountItemName}, '%')))
            </if>
        </where>
        limit 100
    </select>

</mapper>
