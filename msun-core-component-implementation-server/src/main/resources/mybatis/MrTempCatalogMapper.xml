<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.emr.dao.BatchCatalogMapper">
    <sql id="baseSql">
        catalog_id, 
        catalog_name, 
        catalog_des, 
        modifier, 
        modify_time, 
        pid, 
        catalog_num, 
        levels, 
        party_id, 
        status, 
        hospital_id, 
        dept_id, 
        type, 
        rank, 
        his_org_id, 
        his_creater_id, 
        his_creater_name, 
        his_create_time, 
        his_updater_id, 
        his_update_time, 
        version
    </sql>
</mapper>