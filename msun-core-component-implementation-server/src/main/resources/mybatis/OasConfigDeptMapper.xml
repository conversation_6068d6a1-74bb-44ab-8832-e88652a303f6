<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.aims.mapper.OasConfigMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        insert into oas.oas_config_dept(config_dept_id,dept_id,dept_name,dept_type,to_dept_ids,to_dept_names,exe_dept_ids,exe_dept_names,his_org_id,hospital_id,
        his_creater_id,his_creater_name,his_create_time,his_updater_id,his_update_time,version,default_flag) values
        <foreach collection ="list" item="tmpData" index= "index" separator =",">
            (
            #{tmpData.configDeptId}, #{tmpData.deptId}, #{tmpData.deptName},#{tmpData.deptType},#{tmpData.toDeptIds}
            ,#{tmpData.toDeptNames}
            ,#{tmpData.exeDeptIds}
            ,#{tmpData.exeDeptNames}
            ,#{tmpData.hisOrgId}
            ,#{tmpData.hospitalId}
            ,#{tmpData.hisCreaterId}
            ,#{tmpData.hisCreaterName}
            ,now()
            ,#{tmpData.hisUpdaterId}
            ,now()
            ,0
            ,#{tmpData.defaultFlag}
            )
        </foreach>
    </insert>

    <delete id="deleteByPk">
        delete from oas.oas_config_dept where config_dept_id in (
        <foreach collection="list" item="tmpData" index="index" separator=",">
            #{tmpData}
        </foreach>)
    </delete>

    <select id="selectByParams" parameterType="java.util.Map" resultType="java.util.Map">
        select * from oas.oas_config_dept where hospitalId = #{hospitalId}
    </select>

</mapper>
