<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.aims.mapper.OasPathManagementMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        insert into oas.oas_path_management(oas_path_management_id,
        path_name,
        path_name_input_code,
        path_name_full_code,
        remark,
        dept_id,
        dept_name,
        role_id,
        role_name,
        expend,
        version,
        apply_type,
        role_code,
        path_code,
        his_create_time,
        his_update_time,
        his_creater_id,
        his_creater_name,
        invalid_flag,
        hospital_id,
        his_org_id
        ) values
        <foreach collection ="list" item="tmpData"  index= "index" separator =",">
(
            #{tmpData.oasPathManagementId},
            #{tmpData.pathName},
            #{tmpData.pathNameInputCode},
            #{tmpData.pathNameFullCode},
             #{tmpData.remark},
            #{tmpData.deptId},
            #{tmpData.deptName},
            #{tmpData.roleId},
            #{tmpData.roleName},
            #{tmpData.expend},
             0,
            #{tmpData.applyType},
            #{tmpData.roleCode},
            #{tmpData.pathCode},
             now(),
             now(),

             0,
             '交付',
             '1',
            #{tmpData.hospitalId},
            #{tmpData.orgId})
        </foreach>
    </insert>
    <!--and path_code in ('1','2')-->
    <delete id="deletePathManage">
        delete from oas.oas_path_management where hospital_id = #{hospitalId} and his_org_id = #{hisOrgId}
    </delete>

    <delete id="deletePathManageByIds" parameterType="java.util.List">
        delete from oas.oas_path_management where
        oas_path_management_id in (
        <foreach collection="idList" item="tmpData" index="index" separator=",">
            #{tmpData}
        </foreach>)
    </delete>

    <delete id="deletePathNodeByPathCode">
      delete from oas.oas_path_node where hospital_id = #{hospitalId} and his_org_id = #{hisOrgId}
    </delete>
</mapper>
