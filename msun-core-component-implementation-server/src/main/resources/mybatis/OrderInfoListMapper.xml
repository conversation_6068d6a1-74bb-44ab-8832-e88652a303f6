<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.deviceanaysis.dao.OrderInfoListMapper">

    <select id="getOrderInfoList"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictOrder">

        SELECT a.dict_order_id,
               a.order_code,
               a.order_name,
               a.order_class,
               a.input_code,
               a.full_code,
               a.edit_type,
               a.memo,
               a.spec,
               a.order_no,
               a.order_select_limit,
               a.order_unit_id,
               a.order_unit_name,
               a.exe_dept_id,
               a.price,
               a.order_category_id,
               a.modify_flag,
               c.dict_nurse_order_id,
               b.dept_name   as exeDeptName,
               a.out_flag    as flagOut,
               a.clinic_flag as flagClinic,
               dno.nurse_order_name,
               dno.price     as nurseOrderPrice
        FROM comm.dict_order a
                 LEFT JOIN comm.dept b on b.dept_id = a.exe_dept_id
                 INNER JOIN comm.dict_order_doc_vs_nurse as c on a.dict_order_id = c.dict_order_id
                 LEFT JOIN comm.dict_nurse_order as dno on c.dict_nurse_order_id = dno.dict_nurse_order_id
        where a.invalid_flag != '1'
          and a.hospital_id = #{hospitalId}
          and a.his_org_id = #{orgId}
          and (a.order_category_id between 1000 and 1999)
    </select>
</mapper>