<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.PacsReportDocTemplateMapper">



    <select id="getPascDeptId"
            resultType="Long">
              select sr.dept_id  from  chis.pacs.study_room sr
              left join  chis.pacs.sys_dept sd on sr.dept_id =sd.dept_id
              where sr.modality = #{modality} and sr.hospital_id = #{hospitalId}
              and sr.invalid_flag ='0'
              limit 1
    </select>




    <insert id="bachAddListTem" parameterType="com.msun.core.component.implementation.api.imsp.dto.ReportDocTemplateDto">
        INSERT INTO chis.pacs.report_doc_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
        doc_template_id,
        pid,
        template_name,
        type,
        dept_id,
        user_sys_id,
        modality,
        sort_no,
        invalid_flag,
        hospital_id,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        version
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">

            #{docTemplateId},
            #{pid},
            #{templateName},
            #{type},
            #{deptId},
            #{userSysId},
            #{modality},
            #{sortNo},
            #{invalidFlag},
            #{hospitalId},
            #{hisOrgId},
            #{hisCreaterId},
            #{hisCreaterName},
            #{hisCreateTime},
            #{hisUpdaterId},
            #{hisUpdateTime},
            #{version}
        </trim>
    </insert>


    <insert id="bachAddListTemCon" parameterType="com.msun.core.component.implementation.api.imsp.dto.ReportDocTemplateContentDto">
        INSERT INTO chis.pacs.report_doc_template_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
        template_content_id,
        doc_template_id,
        finding,
        conclusion,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        version,
        template_flag,
        content
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{templateContentId},
            #{docTemplateId},
            #{finding},
            #{conclusion},
            #{hisOrgId},
            #{hisCreaterId},
            #{hisCreaterName},
            #{hisCreateTime},
            #{hisUpdaterId},
            #{hisUpdateTime},
            #{version},
            #{templateFlag},
            #{content}
        </trim>
    </insert>


</mapper>