<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.importdata.dao.PatientMapper">
    <!-- 根据患者信息查询对应的字典id数据 -->
    <select id="getPatInfoByParamer" resultType="java.util.Map" flushCache="true">
        select
        coalesce((select max(dict_sex_id) from comm.dict_sex where dict_sex_name = #{sexName}),null) as dict_sex_id,
        coalesce((select max(dict_nation_id) from comm.dict_nation where dict_nation_name = #{nationName}),null) as dict_nation_id,
        coalesce((select max(dict_province_id) from comm.dict_province where  invalid_flag = '0' and dict_province_name = #{provinceName}),null) as dict_province_id,
        coalesce((select max(dict_city_id) from comm.dict_city where invalid_flag = '0' and dict_city_name = #{cityName}),null) as dict_city_id,
        coalesce((select max(dict_town_id) from comm.dict_town where invalid_flag = '0' and dict_town_name = #{townName}),null) as dict_town_id
        <!--coalesce((select max(charge_class_id) from comm.dict_charge_class where charge_class_name = #{chargeClassName}),null) as charge_class_id-->
    </select>
    <!--查询病区和床号-->
    <select id="getDictWardAndBedList" resultType="java.util.Map">
      select
          wd.ward_name || bd.bed_no as "name",
          bd.bed_id as "bedId"
        from comm.ward wd
        inner join zy.bed bd on wd.ward_id =bd.ward_id
        where  wd.hospital_id=#{hospitalId}
        and  wd.his_org_id=#{hisOrgId}
        and wd.invalid_flag='0'
    </select>
    <!--循环查询数据库数据-->
    <select id="getPatientDTOListIds" resultType="java.lang.Long">
        select
        pat_id
        from comm.patient
        where 1=1
        <if test="listIds !=null and listIds.size() > 0">
            and  pat_id in
            <foreach collection="listIds" item="items" open="(" separator="," close=")" index="s">
                #{items}
            </foreach>
        </if>
        <if test="listIds == null">
            and  pat_id = -1
        </if>
        and hospital_id = #{hospitalId}
        and invalid_flag = '0'
    </select>
    <!--获取传过来的校验数据库是否存在card的主键-->
    <select id="getPatientDTOCardListIds" resultType="java.lang.Long" flushCache="true">
        select
        pat_card_id
        from comm.pat_card
        where 1=1
        <if test="listCardIds !=null and listCardIds.size() > 0">
            and  pat_id in
            <foreach collection="listCardIds" item="items" open="(" separator="," close=")" index="s">
                #{items}
            </foreach>
        </if>
        <if test="listCardIds == null">
            and  pat_id = -1
        </if>
        and hospital_id = #{hospitalId}
    </select>

</mapper>