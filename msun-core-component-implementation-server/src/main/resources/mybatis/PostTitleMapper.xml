<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.PostTitleMapper">
    <select id="selectDictPostTitle" resultType="com.msun.core.component.implementation.server.imsp.entity.po.DictPostTitle">
        select post_title_id   as postTitleId,
               post_title_name as postTitleName,
               input_code   as inputCode,
               full_code    as fullCode,
               invalid_flag as invalidFlag
        from comm.dict_post_title
        where invalid_flag = '0'
          and (his_org_id = -1
            or
               his_org_id = #{hisOrgId})
    </select>
</mapper>