<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.report.dao.ReportMainDictMapper">

    <insert id="reportInsert">
        ${value}
    </insert>

    <update id="reportUpdate" parameterType="com.msun.core.component.implementation.server.report.entity.ReportMainPO">
        update msunreportcloud.reportfile
        set his_update_time=now(),reporthtml=#{reportMainPO.reporthtml}
        where report_file_id =
              (SELECT F.report_file_id
               FROM msunreportcloud.msunreportmain M
                        LEFT JOIN msunreportcloud.report_style_info S ON M.report_main_id = S.report_main_id
                        left join msunreportcloud.reportfile F ON F.report_file_id = S.report_file_id
               where M.report_main_id = #{reportMainPO.reportMainId})
    </update>

    <delete id="reportDelete" parameterType="java.lang.String">
        ${value}
    </delete>
    <select id="findReportReportHtml"
            resultType="com.msun.core.component.implementation.server.report.entity.ReportMainPO">
        SELECT F.reporthtml,M.report_main_id
        FROM msunreportcloud.msunreportmain M
        LEFT JOIN msunreportcloud.report_style_info S ON M.report_main_id = S.report_main_id
        left join msunreportcloud.reportfile F ON F.report_file_id = S.report_file_id
        where M.report_main_id in
        <foreach collection="reportMainIds" item="item"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getReportSystemVersionNumber" resultType="java.lang.String">
        select app_version from msunreportcloud.script_log order by update_time desc limit 1;
    </select>

</mapper>