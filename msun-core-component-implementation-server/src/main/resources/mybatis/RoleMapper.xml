<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.RoleMapper">
    <select id="selectRoleByMap" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Role">
        SELECT
            role_id,
            role_name,
            parent_id,
            sort_order,
            role_code,
            LEVEL,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            hospital_id,
            role_type,
            invalid_flag
        FROM
            comm.ROLE
        WHERE
            invalid_flag = '0'
            AND ( his_org_id = - 1 OR his_org_id = #{ his_org_id } )
    </select>
    <!--先查询角色的hisOrgId为-1-->
    <select id="getRoleOne" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Role">
       SELECT
            role_id,
            role_name,
            parent_id,
            sort_order,
            role_code,
            LEVEL,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            hospital_id,
            role_type,
            invalid_flag
        FROM
            comm.ROLE
        WHERE
            invalid_flag = '0'
            AND hospital_id = - 1
            AND his_org_id = #{hisOrgId}
            AND role_name = #{roleName}
    </select>
    <!--根据查询his_org_id本医院的数据-->
    <select id="getHospitalIdRole" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Role">
        SELECT
            role_id,
            role_name,
            parent_id,
            sort_order,
            role_code,
            LEVEL,
            VERSION,
            his_org_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            hospital_id,
            role_type,
            invalid_flag
        FROM
            comm.ROLE
        WHERE
            invalid_flag = '0'
            AND hospital_id = #{hospitalId}
            AND his_org_id = #{hisOrgId}
            AND role_name = #{roleName}
    </select>
</mapper>