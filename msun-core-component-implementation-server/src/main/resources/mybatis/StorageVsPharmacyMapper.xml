<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.StorageVsPharmacyMapper">



    <select id="findPharmacyByStorageDeptSet" resultType="StorageVsPharmacy">
        select
            storage_dept_id,
            storage_dept_name,
            pharmacy_dept_id,
            pharmacy_dept_name
        from
            res.storage_vs_pharmacy
        where
          storage_dept_id in
            <foreach collection="ykDeptIdSet" index="index" item="item"
                 open="(" separator="," close=")">
             #{item}
            </foreach>
          and invalid_flag='0'
    </select>

    <select id="findPharmacyByStorageDeptId" resultType="StorageVsPharmacy">
        select
        storage_dept_id,
        storage_dept_name,
        pharmacy_dept_id,
        pharmacy_dept_name
        from
        res.storage_vs_pharmacy
        where
        storage_dept_id = #{ykDeptId}
        and invalid_flag='0'
    </select>

</mapper>