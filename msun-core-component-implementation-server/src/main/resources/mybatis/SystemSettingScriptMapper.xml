<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.SystemSettingScriptMapper">
    <insert id="pacsScriptOneSql">
        INSERT INTO chis.comm.bodypart
        (bodypart_id,
         bodypart_name,
         english_name,
         input_code,
         other_code,
         modality,
         standard_id,
         parent_id,
         orientation,
         order_no,
         tree_depth,
         invalid_flag,
         use_flag,
         hospital_id,
         his_org_id,
         his_creater_id,
         his_creater_name,
         his_create_time,
         his_updater_id,
         his_update_time,
         "version")

            (
            SELECT
                a.dict_order_id bodypart_id,
                a.order_name bodypart_name,
                '' english_name,
                a.input_code,
                a.full_code other_code,
                case
                    when order_category_id=2004 then 'US'
                    WHEN order_category_id=2003 then 'ES'
                    WHEN order_category_id=2008 then 'MG'
                    WHEN order_category_id=2000 then 'CT'
                    WHEN order_category_id=2001 then 'CR'
                    WHEN order_category_id=2002 then 'RF'
                    WHEN order_category_id=2005 then 'MR'
                    WHEN order_category_id=2006 then 'XA'
                    WHEN order_category_id=2017 then 'DX'
                    WHEN order_category_id=2016 then 'NM'
                    WHEN order_category_id=2007 then 'ESENT'
                    WHEN order_category_id=2009 then 'LS'
                    WHEN order_category_id=2010 then 'FOB'
                    WHEN order_category_id=2012 then 'PX'
                    WHEN order_category_id=2013 then 'EEG'
                    WHEN order_category_id=2018 then 'TCD'
                    END as modality,
                0 standard_id,
                0 parent_id,
                0 orientation,
                0 order_no,
                0 tree_depth,
                a.invalid_flag,
                '0' use_flag,
                a.hospital_id,
                a.his_org_id,
                a.his_creater_id,
                a.his_creater_name,
                a.his_create_time,
                a.his_updater_id,
                a.his_update_time,
                a.version
            FROM
                chis.comm.dict_order a
                    left join chis.comm.dept b
                              on a.exe_dept_id=b.dept_id
            where b.category_id='6'
              and a.invalid_flag='0'
              and order_category_id between '2000' and '2020'
              and a.hospital_id=#{hospitalId}
            order by order_code)
    </insert>
    <insert id="pacsScriptTwoSql">
        insert into  chis.comm.bodypart_vs_order
        (bodypart_vs_order_id,
         bodypart_id,
         order_id,
         his_org_id,
         his_creater_id,
         his_creater_name,
         his_create_time,
         his_updater_id,
         his_update_time,
         version,
         hospital_id)
            (SELECT
                 bodypart_id bodypart_vs_order_id,
                 bodypart_id,
                 bodypart_id order_id,
                 his_org_id,
                 his_creater_id,
                 his_creater_name,
                 his_create_time,
                 his_updater_id,
                 his_update_time,
                 "version",
                 hospital_id
             FROM
                 chis.comm.bodypart  where invalid_flag='0'
                                       and hospital_id=#{hospitalId})
    </insert>
    <insert id="pacsScriptThreeSql">
        INSERT INTO chis.pacs.sys_hospital
        (hospital_id,
         hospital_code,
         hospital_name,
         alias_name,
         input_code,
         input_alias_code,
         full_code,
         full_alias_code,
         invalid_flag,
         parent_id,
         area_code,
         address,
         hospital_level,
         organization_code,
         introduction,
         version,
         his_org_id,
         his_creater_id,
         his_creater_name,
         his_create_time,
         his_updater_id,
         his_update_time,
         ae_title)
            (
            select hospital_id,hospital_code,hospital_name,alias_name,input_code,input_alias_code,
                   full_code,full_alias_code,invalid_flag,parent_id,area_code,address,hospital_level,
                   organization_code,introduction,version,his_org_id ,his_creater_id,his_creater_name,his_create_time,
                   his_updater_id,his_update_time,'ZYPACS' || hospital_id ae_title
            from chis.comm.hospital where hospital_id=#{hospitalId});
    </insert>
    <insert id="pacsScriptForeSql">
        insert  into chis.pacs.sys_dept
        (dept_id,
         hospital_id,
         dept_code,
         dept_name,
         alias_name,
         input_code,
         input_alias_code,
         full_code,
         full_alias_code,
         version,
         his_org_id,
         his_creater_id,
         his_creater_name,
         his_create_time,
         his_updater_id,
         his_update_time,
         hospital_name)

            (
            select a.dept_id,a.hospital_id,dept_code,dept_name,a.alias_name,a.input_code,
                   a.input_alias_code,a.full_code,a.full_alias_code,a.version,a.his_org_id,a.his_creater_id,
                   a.his_creater_name,a.his_create_time,a.his_updater_id,a.his_update_time,b.hospital_name from chis.comm.dept a
                                                                                                                    left join chis.comm.hospital b
                                                                                                                              on a.hospital_id=b.hospital_id
            where dept_id in(select distinct exe_dept_id from chis.comm.dict_order where order_category_id between '2000' and '2020' and exe_dept_id is not null and exe_dept_id!='-1')
            and category_id='6' and a.hospital_id=#{hospitalId});
    </insert>
    <update id="wisdomEmergencyScriptOneSql">
        update chis.emis.sys_dic_item set invalid_flag = 1,his_update_time = NOW() where type_id = (select id from chis.emis.sys_dic_type where code = 'triageDept');
    </update>
    <insert id="wisdomEmergencyScriptTwoSql">
        insert into chis.emis.sys_dic_item(id,type_id,store_val,show_val,his_update_time)
        select dept_id,(select id from chis.emis.sys_dic_type where code = 'triageDept' order by his_create_time desc limit 1) , dept_id,dept_name,his_update_time from chis.comm.dept  where dept_name like '%急诊%' and category_id ='1' and invalid_flag &lt;&gt; '1';
    </insert>
    <insert id="wisdomEmergencyScriptThreeSql">
        insert into chis.comm.config_scope (config_scope_id,config_id,hospital_id,dept_id,version,config_type,config_value,his_org_id,his_creater_id,his_creater_name,his_create_time,his_updater_id,his_update_time)
        select dept_id,(select config_id from chis.comm.config where config_code  =  'SpecialistMedicalRecords'),hospital_id,dept_id,0,'string','1',his_org_id,his_creater_id,his_creater_name,his_create_time,his_updater_id,his_update_time from chis.comm.dept where dept_name like '%急诊%' and invalid_flag = '0' and category_id = '1';
    </insert>
    <insert id="wisdomEmergencyScriptForeSql">
        update chis.comm.config_out_dept set dept_register_flag = 1,his_update_time = NOW() where dept_id in (
            select dept_id from chis.comm.dept where category_id = '1' and dept_name like '急诊%');
    </insert>

    <update id="medicalRecordScriptSql">
        update chis.comm.config
        set config_value=#{medicalType}
        where config_code='bingAnOpenType';
    </update>


    <!--查询配置表有没有配置-->
    <select id="countConfigByCode" resultType="int">
        select count(*) from  chis.comm.config where config_code=#{configCode} limit 1
    </select>
    <select id="findValueByCode" resultType="string">
        select config_value from  chis.comm.config where config_code=#{configCode}
    </select>
    <!--根据code更新值-->
    <update id="updateConfigValueByCode">
        update chis.comm.config
        set config_value= #{configValue},
           his_update_time = now()
        where config_code=#{configCode}
    </update>
    <!--新增配置-->
    <insert id="saveConfig" parameterType="com.msun.core.component.implementation.server.imsp.entity.po.Config">
        insert into chis.comm.config (config_id, business_type_id, config_code, config_name,
                            display_flag, maintain_flag, config_type, input_code,
                            full_code, precondition, memo, sort_order,
                            version, config_value, his_org_id, his_creater_id,
                            his_creater_name, his_create_time, his_updater_id, his_update_time)
        values (#{configId}, #{businessTypeId}, #{configCode}, #{configName},
                 #{displayFlag}, #{maintainFlag}, #{configType}, #{inputCode},
                 #{fullCode}, #{precondition}, #{memo}, #{sortOrder},
                 #{version}, #{configValue}, #{hisOrgId}, #{hisCreaterId},
                 #{hisCreaterName}, now(), #{hisUpdaterId}, now())
    </insert>

    <select id="findIdentityCountByRuleCode" resultType="int">
        select count(a.*) from chis.comm.identity a  where a.role_id in (select b.role_id from chis.comm.role b where b.role_code=#{roleCode})
    </select>

    <select id="queryProductAuthResult"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductAuthResultDTO">
        select hospital_id,
               a.published_system_code,
               published_system_menu_code,
               published_system_standard_name,
               published_system_id,
               min(his_create_time) as "firstAuthTime"
        from (select distinct(ps.published_system_code),
                             ps.published_system_menu_code,
                             ps.published_system_standard_name,
                             ps.published_system_id,
                             ai.hospital_id,
                             ai.his_create_time
              from comm.system_authorize_info ai
                       left join comm.published_system ps on ai.published_system_id = ps.published_system_id
              where ai.invalid_flag = '0'
                and ps.invalid_flag = '0') a
        group by a.published_system_code, published_system_menu_code, published_system_standard_name,
                 published_system_id, hospital_id
        order by hospital_id, published_system_id
    </select>
</mapper>
