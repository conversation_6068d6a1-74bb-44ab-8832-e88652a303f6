<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.TigcTackAreaMapper">

    <select id="selectTigcArea"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.FzClinicArea">
        SELECT
            clinic_area_id
        FROM
            tigc.fz_clinic_area
        WHERE
            hospital_id =#{hospitalId}
    </select>

</mapper>