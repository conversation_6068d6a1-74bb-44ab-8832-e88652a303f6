<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.TigcTackDeptMapper">

    <select id="selectTigcDept" resultType="java.lang.Long">
        SELECT
            relation_id
        FROM
            tigc.fz_room_vs_dept
        WHERE
            hospital_id =#{hospitalId}
    </select>
    <select id="selectDeptOfCall" resultType="java.lang.String">
        select d.dept_name from comm.dept d left join comm.config_out_dept b on d.dept_id =b.dept_id
        where d.category_id ='1'
          and d.invalid_flag ='0'
          and b.triage_flag ='0'
          and d.hospital_id =#{hospitalId}
    </select>

</mapper>