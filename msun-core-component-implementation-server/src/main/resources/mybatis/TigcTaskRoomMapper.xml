<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.TigcTackRoomMapper">

    <select id="selectTigcRoom"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.FzRoom">
        SELECT
            room_id,
            scene_type,
            scene_subtype
        FROM
            tigc.fz_room
        WHERE
            hospital_id =#{hospitalId}
    </select>

</mapper>