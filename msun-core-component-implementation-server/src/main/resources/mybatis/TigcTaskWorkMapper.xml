<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.TigcTackWorkMapper">

    <select id="selectTigcWork"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.FzWorkstation">
        SELECT
            workstation_id,
            workstation_name,
            scene_type,
            scene_subtype,
            workstation_machine_code,
            ip,
            workstation_mac
        FROM
            tigc.fz_workstation
        WHERE
            hospital_id =#{hospitalId} and delete_flag = '0'
    </select>

</mapper>