<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.UserMapper">
    <sql id="baseSql">
        t.user_id
        , t.dept_id
        , t.user_code
        , t.user_name
        , t.invalid_flag
        , t.input_code
        , t.full_code
        , t.level
        , t.regular_flag
        , t.id_card
        , t.telphone
        , t.introduction
        , t.specialty
        , t.qualification_code
        , t.wb_code
        , t.version
        , t.dept_name
        , t.hospital_id
        , t.hospital_name
        , t.his_org_id
        , t.his_creater_id
        , t.his_creater_name
        , t.his_create_time
        , t.his_updater_id
        , t.his_update_time
        , t.post_title_id
        , t.red_signature
        , t.black_signature
        , t.signature_status
        , t.msun_staff_code
        , t.qualification_certificate_code
        , t.practice_certificate_code
        , t.staff_id
        , t.medical_insurance_code
        , t.user_account_name
    </sql>

    <!-- 更新不为NULL的字段 -->
    <update id="updateIgnoreNull" parameterType="com.msun.core.component.implementation.server.imsp.entity.po.User">
        UPDATE comm.user u
        <set>
            <if test="hisCreaterId != null">
                his_creater_id=#{hisCreaterId},
            </if>
            <if test="hisCreaterName != null">
                his_creater_name=#{hisCreaterName},
            </if>
            <if test="hisCreateTime != null">
                his_create_time=#{hisCreateTime},
            </if>
            <if test="hisUpdaterId != null">
                his_updater_id=#{hisUpdaterId},
            </if>
            <if test="hisUpdateTime != null">
                his_update_time=#{hisUpdateTime},
            </if>
            <if test="redSignature != null" >
                red_signature=#{redSignature},
            </if>
            <if test="blackSignature != null" >
                black_signature=#{blackSignature},
            </if>
        </set>
        WHERE u.user_code = #{userCode}
        and u.hospital_id = #{hospitalId}
        and u.invalid_flag = '0'
    </update>
    <select id="getTotalCount" resultType="java.lang.Integer">
        select count(*) from comm.user where invalid_flag = '0'
    </select>
    <select id="getUserCodeList" resultType="java.lang.String" parameterType="java.util.Map">

        select user_code from
            comm.user
        where invalid_flag = '0'
            limit #{max_value}  offset  #{limit}
    </select>
</mapper>
