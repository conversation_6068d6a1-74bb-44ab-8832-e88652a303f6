<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.WardMapper">

    <select id="selectWardByWardName" resultType="com.msun.core.component.implementation.server.imsp.entity.po.Ward">
        select ward_id from comm.ward where
        ward_name = #{wardName}
        and his_org_id = #{orgId}
        and hospital_id = #{hospitalId}
    </select>
</mapper>