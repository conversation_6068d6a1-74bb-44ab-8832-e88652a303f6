<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--字典数据-->
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.WithQuantityPurchaseBatchMapper">
   <!--查询采购字典所有数据-->
    <select id="getWithQuantityPurchaseBatchAll" resultType="com.msun.core.component.implementation.server.imsp.entity.po.WithQuantityPurchaseBatchPo">
        select
            with_quantity_purchase_batch_id,
            with_quantity_purchase_batch_name,
           his_org_id,
           his_creater_id
        from comm.with_quantity_purchase_batch
        where invalid_flag='0'
        and his_org_id=#{hisOrgId}
    </select>


    <select id="selectWithQuantityPurchaseBatchList" resultType="com.msun.core.component.implementation.server.imsp.entity.po.WithQuantityPurchaseBatchPo">
        select
            with_quantity_purchase_batch_id,
            with_quantity_purchase_batch_name,
            his_org_id,
            his_creater_id
        from comm.with_quantity_purchase_batch
        where invalid_flag='0'
          and (his_org_id= -1 or his_org_id=#{hisOrgId})
    </select>

</mapper>
