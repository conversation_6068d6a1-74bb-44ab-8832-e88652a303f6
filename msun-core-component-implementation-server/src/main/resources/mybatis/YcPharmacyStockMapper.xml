<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.YcPharmacyStockMapper">


    <update id="updateYfStockQuantityById">
        UPDATE
                res.yc_pharmacy_stock
        SET quantity = quantity + #{quantity},
            his_update_time = cast(current_timestamp(3) as timestamp without time zone)
        WHERE pharmacy_stock_id = #{pharmacyStockId}
          and quantity + #{quantity} >= 0
    </update>


</mapper>