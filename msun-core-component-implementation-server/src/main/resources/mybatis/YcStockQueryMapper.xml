<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.drug.dao.YcStockQueryMapper">


    <update id="updateQueryQuantityById">
        UPDATE
                res.yc_stock_query
        SET stock_amount = stock_amount + #{quantity},
            his_updater_id  = #{userSysId},
            his_update_time = cast(current_timestamp(3) as timestamp without time zone)
        WHERE yc_stock_query_id = #{ycStockQueryId}
          and stock_amount + #{quantity} >= 0
    </update>


    <update id="updateYkStockQuantityById">
        UPDATE
                res.yc_storage_stock
        SET quantity = quantity + #{quantity},
            his_updater_id = #{userSysId},
            his_update_time = cast (current_timestamp(0) as  timestamp without time zone)
        WHERE storage_stock_id = #{storageStockId}
          and quantity + #{quantity} >= 0
    </update>


    <select id="getStockQueryByMerge" resultType="YcStockQueryPO">
        SELECT
        MAX( sq.yc_stock_query_id ) AS yc_stock_query_id
        FROM
        res.yc_stock_query sq
        <where>
            <if test="mergeStockQueryDTO.ycId != null">
                and sq.yc_id =#{mergeStockQueryDTO.ycId}
            </if>
            <if test="mergeStockQueryDTO.drugSpecPackingId != null">
                and sq.drug_spec_packing_id =#{mergeStockQueryDTO.drugSpecPackingId}
            </if>
            <if test="mergeStockQueryDTO.packingId != null">
                and sq.packing_id =#{mergeStockQueryDTO.packingId}
            </if>
            <if test="mergeStockQueryDTO.retailPrice != null ">
                and sq.retail_price =#{mergeStockQueryDTO.retailPrice}
            </if>
            <if test="mergeStockQueryDTO.buyPrice != null">
                and sq.buy_price =#{mergeStockQueryDTO.buyPrice}
            </if>
            <if test="mergeStockQueryDTO.tradePrice != null ">
                and sq.trade_price =#{mergeStockQueryDTO.tradePrice}
            </if>
            <if test="mergeStockQueryDTO.producerId != null">
                and sq.producer_id =#{mergeStockQueryDTO.producerId}
            </if>
            <if test="mergeStockQueryDTO.ycDeptId != null ">
                and sq.yc_dept_id =#{mergeStockQueryDTO.ycDeptId}
            </if>
        </where>
        group by
        <if test="mergeStockQueryDTO.drugSpecPackingId != null">
            sq.drug_spec_packing_id,
        </if>
        <if test="mergeStockQueryDTO.packingId != null">
            sq.packing_id,
        </if>
        <if test="mergeStockQueryDTO.retailPrice != null ">
            sq.retail_price,
        </if>
        <if test="mergeStockQueryDTO.buyPrice != null">
            sq.buy_price,
        </if>
        <if test="mergeStockQueryDTO.tradePrice != null ">
            sq.trade_price,
        </if>
        <if test="mergeStockQueryDTO.producerId != null">
            sq.producer_id,
        </if>
        <if test="mergeStockQueryDTO.ycDeptId != null ">
            sq.yc_dept_id,
        </if>
        <if test="mergeStockQueryDTO.ycId != null">
            sq.yc_id
        </if>
    </select>


    <select id="getDrugPackingById" resultType="YcStockQueryPO">
        select
        sq.yc_stock_query_id as stockQueryId,
        sq.yc_id,
        dbm.drug_name as ycName,
        dbm.min_packing_id as minPackingId,
        dp.packing_name as minPackingName,
        sq.drug_spec_packing_id as nowDrugSpecPackingId
        from res.yc_stock_query sq
        left join comm.dict_drug_basic_main dbm on dbm.drug_basic_main_id = sq.yc_id
        left join comm.dict_drug_packing dp on dp.packing_id = dbm.min_packing_id
        <where>
            sq.yc_stock_query_id = #{stockQueryId}
        </where>
    </select>

</mapper>