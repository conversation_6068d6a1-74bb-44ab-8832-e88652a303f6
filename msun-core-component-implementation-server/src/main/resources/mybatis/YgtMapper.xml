<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.ygt.dao.YgtMapper">

    <!-- 新增机构系统域名 -->
    <insert id="syncSystemDomainInfo">
        insert into ygt.bd_hospital_system(system_id, system_name, system_interface_url, his_create_time,
                                           his_update_time)
        values (#{systemId}, #{systemName}, #{systemInterfaceUrl}, now(), now())
    </insert>
</mapper>