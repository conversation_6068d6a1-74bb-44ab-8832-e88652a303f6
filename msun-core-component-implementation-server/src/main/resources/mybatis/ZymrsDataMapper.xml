<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ZymrsDataMapper">


    <delete id="deleteByHosID">
        delete
        from mr.dict_serial_rule
        where hospital_id = #{hospitalId}
    </delete>
    <select id="getHospitalInfoFromCloud" resultType="java.util.Map">
        select hospital_level,
               organization_code,
               org_type_id,
               country_name,
               province_name,
               town_name,
               city_name,
               address
        from comm.hospital
        where hospital_id = #{param.hospitalId}
          and his_org_id = #{param.hisOrgId}
    </select>
    <select id="getSaveDepartmentList" resultType="java.util.Map">
        SELECT sys_dept_dict_id "sysBaseDictId",
        dept_id "code",
        dept_name "name",
        gjwt_xy_code "gjwtCode",
        gjwt_xy_name "gjwtName",
        sdwt_xy_code "sdwtCode",
        sdwt_xy_name "sdwtName",
        glyy_xy_code "glyyCode",
        glyy_xy_name "glyyName",
        flag_invalid "flagInvalid",
        prefix
        FROM "mr"."sys_dept_dict"
        <where>
            AND hospital_id = #{hospitalId}
        </where>
    </select>
    <select id="getPayMethodDictCompare" resultType="java.util.Map">
        select dict_base_vs_his_id "dictBaseVsHisId",
        base_id "baseId",
        his_id "hisId",
        his_name "hisName",
        usable
        from "mrs"."dict_base_vs_his"
        <where>
            and invalid = '0'
            and new_flag = '1'
            and base_dict_code = 'payMethod'
        </where>
    </select>
</mapper>