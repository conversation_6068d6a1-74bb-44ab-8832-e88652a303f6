<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ZyoIsConfigMapper">
    <update id="updateZyoIsConfig" parameterType="com.msun.core.component.implementation.server.imsp.entity.po.ZyoIsConfig">
        update sc.zyois_config
        set config_value = #{configValue},
        his_update_time =#{hisUpdateTime},
        his_updater_id = #{hisUpdaterId}
        where config_code = #{configCode}
    </update>
</mapper>