<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.bis.dao.BisBaseDataMapper">

    <select id="findHisOrderDict" resultType="com.msun.core.component.implementation.api.imsp.vo.DictOrderVo">
        select a.dict_order_id ,a.order_code ,a.order_name ,a.price ,c.dept_name exe_dept_name
        from comm.dict_order a
        left join comm.dept c on a.exe_dept_id = c.dept_id
        where a.invalid_flag = '0' and a.hospital_id =#{hospitalId}
        <if test="likeInputStr != null and likeInputStr != ''">
            and (a.order_name like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%'))
        </if>
        <if test="objectIds != null and objectIds.size > 0">
            and a.dict_order_id in
            <foreach collection="objectIds" index="index" item="objectId" open="(" separator="," close=")">
                #{objectId}
            </foreach>
        </if>
        order by a.order_no
        limit 100
    </select>

    <select id="findHisChargeDict"
            resultType="com.msun.core.component.implementation.api.bis.vo.BisNurseOrderVO">
        select a.dict_nurse_order_id  ,a.nurse_order_name ,a.price ,c.dept_name exe_dept_name
        from comm.dict_nurse_order a
        left join comm.dept c on a.exe_dept_id = c.dept_id
        where a.invalid_flag = '0' and a.hospital_id =#{hospitalId}
        <if test="likeInputStr != null and likeInputStr != ''">
            and (a.nurse_order_name like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%'))
        </if>
        <if test="objectIds != null and objectIds.size > 0">
            and a.dict_nurse_order_id in
            <foreach collection="objectIds" index="index" item="objectId" open="(" separator="," close=")">
                #{objectId}
            </foreach>
        </if>
        order by a.order_no
        limit 100
    </select>

</mapper>