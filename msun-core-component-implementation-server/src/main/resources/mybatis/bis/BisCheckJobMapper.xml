<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.bis.dao.BisCheckJobMapper">

    <select id="bisCheckJobUserRole" resultType="java.lang.Integer">
        select count(1)
        from comm.role a
        inner join comm.identity b on a.role_id = b.role_id
        where a.role_code = 'bis'
        and a.invalid_flag = '0'
        and b.delete_flag = '0'
        and b.hospital_id = #{hospitalId}
    </select>

    <select id="bisCheckJobLisItem" resultType="java.lang.String">
        select
            bis_dict_lis_item_name
        from (
            select
                a.bis_dict_lis_item_name,max(b.item_id) item_id
            from comm.bis_dict_lisitem_config a
            left join comm.bis_lisitem_vs_lis b on a.bis_dict_lis_item_id = b.bis_dict_lis_item_id and b.invalid_flag = '0'
            where a.invalid_flag = '0'
            and a.bis_dict_lis_item_name in('ABO','Rh(D)','ALT','HBsAg','Anti-HCV','Anti-HIV1/2','Anti-TP','HGB','HCT','PLT')
            and a.hospital_id = #{hospitalId}
            group by a.bis_dict_lis_item_name
        ) m where m.item_id is null
    </select>

    <select id="bisCheckJobCharge" resultType="java.lang.Integer">
        select count(1)
        from comm.bis_dict_blood_charge
        where invalid_flag = '0'
        and hospital_id = #{hospitalId}
    </select>

    <select id="bisCheckJobVarietyScan" resultType="java.lang.Integer">
        select
            count(1)
        from comm.bis_dict_variety_scan_set
        where invalid_flag = '0'
            and hospital_id = #{hospitalId}
    </select>

    <select id="bisCheckJobTypeScan" resultType="java.lang.Integer">
        select
            count(1)
        from comm.bis_dict_type_scan_set
        where invalid_flag = '0'
            and hospital_id = #{hospitalId}
    </select>

    <select id="bisCheckJobBloodInfo" resultType="string">
        select
            string_agg(a.blood_variety_name,',')
        from comm.bis_dict_blood_variety a
        left join comm.bis_dict_blood_information b on b.blood_variety_id = a.blood_variety_id and b.invalid_flag = '0'
        where a.invalid_flag = '0'
            and b.blood_information_id is null
            and a.hospital_id = #{hospitalId}
    </select>

</mapper>