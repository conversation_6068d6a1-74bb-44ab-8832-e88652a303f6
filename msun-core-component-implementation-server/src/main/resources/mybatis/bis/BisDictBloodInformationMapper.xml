<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.bis.dao.BisDictBloodInformationMapper">

    <resultMap type="com.msun.core.component.implementation.server.bis.entity.po.BisDictBloodInformation"
               id="BisDictBloodInformationMap">
        <result property="bloodInformationId" column="blood_information_id" jdbcType="INTEGER"/>
        <result property="bloodVarietyId" column="blood_variety_id" jdbcType="INTEGER"/>
        <result property="bloodType" column="blood_type" jdbcType="VARCHAR"/>
        <result property="bloodRh" column="blood_rh" jdbcType="VARCHAR"/>
        <result property="bridgeId" column="bridge_id" jdbcType="INTEGER"/>
        <result property="bridgeLayer" column="bridge_layer" jdbcType="INTEGER"/>
        <result property="bloodSpec" column="blood_spec" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="dictNurseOrderId" column="dict_nurse_order_id" jdbcType="INTEGER"/>
        <result property="quantity" column="quantity" jdbcType="OTHER"/>
        <result property="inStoragePrice" column="in_storage_price" jdbcType="OTHER"/>
        <result property="irregularAntibody" column="irregular_antibody" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>