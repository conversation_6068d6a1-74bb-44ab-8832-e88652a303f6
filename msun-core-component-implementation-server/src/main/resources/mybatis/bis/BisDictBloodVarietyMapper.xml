<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.bis.dao.BisDictBloodVarietyMapper">

    <resultMap type="com.msun.core.component.implementation.server.bis.entity.po.BisDictBloodVariety"
               id="BisDictBloodVarietyMap">
        <result property="bloodVarietyId" column="blood_variety_id" jdbcType="INTEGER"/>
        <result property="bloodVarietyCode" column="blood_variety_code" jdbcType="VARCHAR"/>
        <result property="bloodVarietyName" column="blood_variety_name" jdbcType="VARCHAR"/>
        <result property="unitId" column="unit_id" jdbcType="INTEGER"/>
        <result property="bloodClassId" column="blood_class_id" jdbcType="INTEGER"/>
        <result property="transfusionRuleId" column="transfusion_rule_id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="validDays" column="valid_days" jdbcType="INTEGER"/>
        <result property="validDaysUnit" column="valid_days_unit" jdbcType="VARCHAR"/>
        <result property="autologousFlag" column="autologous_flag" jdbcType="VARCHAR"/>
        <result property="belongParentType" column="belong_parent_type" jdbcType="VARCHAR"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="inputCode" column="input_code" jdbcType="VARCHAR"/>
        <result property="fullCode" column="full_code" jdbcType="VARCHAR"/>
        <result property="wbCode" column="wb_code" jdbcType="VARCHAR"/>
        <result property="unitName" column="unit_name" jdbcType="VARCHAR"/>
        <result property="conversion" column="conversion" jdbcType="VARCHAR"/>
        <result property="mainMatchResultId" column="main_match_result_id" jdbcType="INTEGER"/>
        <result property="sideMatchResultId" column="side_match_result_id" jdbcType="INTEGER"/>
        <result property="overdueWarningDays" column="overdue_warning_days" jdbcType="INTEGER"/>
        <result property="matchResultId" column="match_result_id" jdbcType="INTEGER"/>
        <result property="massTransfusionFlag" column="mass_transfusion_flag" jdbcType="VARCHAR"/>
        <result property="orderConversion" column="order_conversion" jdbcType="VARCHAR"/>
        <result property="printNodeCode" column="print_node_code" jdbcType="VARCHAR"/>
        <result property="continuousEntryFlag" column="continuous_entry_flag" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findExistBloodVariety" resultType="java.lang.String">
        select blood_variety_name
        from comm.bis_dict_blood_variety
        where hospital_id = #{hospitalName}
    </select>


</mapper>