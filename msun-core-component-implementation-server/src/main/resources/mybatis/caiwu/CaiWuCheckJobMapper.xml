<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.caiwu.dao.CaiWuCheckJobMapper">
    <select id="countNum" resultType="Integer">
        select count(1)
        from comm.scheduling_record
        where del_flag = '0'
    </select>

    <select id="getZyhRuleCountNum" resultType="Integer">
        select count(1)
        from comm.code
        where code_type = 'ZYH'
    </select>

    <select id="getZyhCodeValueCountNum" resultType="Integer">
        select count(1)
        from comm.code_value
        where code_key = #{codeKey}
    </select>
    <select id="getHospital" resultType="com.msun.core.component.implementation.server.caiwu.strategyfactory.entity.po.Hospital">
        SELECT hospital_id,
               hospital_code,
               hospital_name,
               alias_name,
               input_code,
               input_alias_code,
               full_code,
               full_alias_code,
               invalid_flag,
               start_time,
               end_time,
               parent_id,
               area_code,
               address,
               hospital_level,
               organization_code,
               building_area,
               actual_open_bed,
               icu_actual_open_bed,
               jzlg_actual_open_bed,
               introduction,
               "version",
               "level",
               wb_code,
               his_org_id,
               his_creater_id,
               his_creater_name,
               his_create_time,
               his_updater_id,
               his_update_time,
               medical_institution,
               msun_org_id,
               social_credit_code,
               country_id,
               province_id,
               city_id,
               town_id,
               country_name,
               province_name,
               city_name,
               town_name,
               org_type_id,
               group_code,
               group_name,
               drug_limit_type,
               h_sort_order,
               dean_name,
               dean_phone,
               medical_insurance_code,
               online,
               trial_count,
               trial_warn_time,
               online_time,
               online_not_verify_days,
               check_heartbeat_minute,
               work_start_time,
               work_end_time
        FROM comm.hospital
    </select>
    <select id="getConfTallyTimer" resultType="com.msun.core.component.implementation.server.caiwu.strategyfactory.entity.po.ConfigTallyTimerPO">
        select
            conf_tally_timer_id,
            tally_timer_name,
            tally_timer_type,
            business_type,
            tally_type,
            tally_time,
            tally_timer_cron,
            timer_extend_parameter,
            tally_time_disable_set,
            "version",
            his_org_id,
            hospital_id,
            his_creater_id,
            his_creater_name,
            his_create_time,
            his_updater_id,
            his_update_time,
            disable_tally_standard,
            disable_auto_tally_cancel,
            manul_tally_user
        from
            finance.conf_tally_timer
        where
           (hospital_id = #{hospitalId} or hospital_id =-1) and business_type = #{businessType}
    </select>

    <select id="getSettleOperator" resultType="integer">
        select count(dto.third_operator_id)
        from comm.dict_third_operator dto
                 inner join comm."identity" i on i.identity_id = dto.operator_id
        where user_type in (0, 1)
            and (dto.hospital_id = #{hospitalId} or dto.hospital_id =-1)
          and invalid_flag = '0'
    </select>

    <select id="getOperatorList" resultType="integer">
        select
            count(1)
        from
            comm.config c
                left join comm.config_scope cs on cs.config_id = c.config_id
                inner join comm.identity i on i.identity_id = coalesce(cast(cs.config_value as int8),cast(c.config_value as int8))
        where
            config_code = #{configCode} and (i.hospital_id = #{hospitalId} or i.hospital_id =-1)
    </select>
</mapper>