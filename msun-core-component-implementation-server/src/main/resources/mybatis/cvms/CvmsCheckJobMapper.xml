<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.cvms.dao.CvmsCheckJobMapper">
    <select id="getUserPhoneNum" resultType="float">
        select round((select count(1) from chis.comm.user where telphone != '' and hospital_id = #{hospitalId} and his_org_id = #{hisOrgId})::numeric /
        (select count(1) from chis.comm.user where  hospital_id = #{hospitalId} and his_org_id = #{hisOrgId}), 3) bl
    </select>

    <select id="getDeptPhoneNum" resultType="float">
        select round((select count(1)
                      from chis.comm.dept
                      where phone != '' and hospital_id = #{hospitalId} and his_org_id = #{hisOrgId})::numeric /
    (select count(1) from chis.comm.dept where hospital_id = #{hospitalId} and his_org_id = #{hisOrgId}),
                     3) bl
    </select>
    <select id="getPacsConfigNum" resultType="int">
        select count(*)
        from chis.pacs.sys_config sc
        where param_key = 'sendMessageToCvms'
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>
</mapper>