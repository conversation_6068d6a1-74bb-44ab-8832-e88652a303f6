<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.emis.dao.PatVitalSignsMapper">
    <!--查询是否有监护仪数据-->
    <select id="checkMonitorIsConnect" resultType="java.lang.Integer">
        SELECT count(1)
        FROM emis.emis_pat_vital_signs epvs
        WHERE (epvs.heart_beat is not null and epvs.heart_beat != -1)
           or (epvs.pressure_close is not null and epvs.pressure_close != -1)
           or (epvs.pressure_open is not null and epvs.pressure_open != -1)
           or (epvs.pulse is not null and epvs.pulse != -1)
           or (epvs.spo2 is not null and epvs.spo2 != -1)
    </select>
    <select id="checkConfigValue" resultType="java.lang.Integer">
        select  sum(configValue)  from  (
              select  count(1) configValue  from  comm.config con  where  config_code='PatientVisitGoBYEmergencyTriage'  and  config_value='1' and  display_flag='1'
                union all
               select  count(1) configValue  from   comm.config_scope  where    config_id=(select  config_id  from   comm.config  con where  con.config_code='PatientVisitGoBYEmergencyTriage' and   con.config_value='1' and   con.display_flag='1')
                ) a

    </select>
</mapper>