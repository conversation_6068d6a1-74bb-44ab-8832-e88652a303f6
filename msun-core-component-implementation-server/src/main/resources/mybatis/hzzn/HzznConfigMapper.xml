<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.hzzn.dao.HzznConfigMapper" >
    <sql id="baseSql">
        c.config_id as configId,
        c.config_code as configCode,
        c.config_name as configName,
        c.business_type_id as businessTypeId,
        c.precondition as precondition,
        c.config_type as configType,
        c.sort_order as sortOrder,
        c.display_flag as displayFlag,
        c.maintain_flag as maintainFlag,
        c.config_value as configValue,
        c.memo as memo,
        c.version as version
    </sql>

    <!--根据配置编码批量获取配置信息-->
    <select id="getConfigByCode" resultType="com.msun.core.component.implementation.server.hzzn.entity.HzznConfig">
        select
        <include refid="baseSql"/>
        from
        comm.config c
        where
        c.config_code=#{code}
        limit 1
    </select>
</mapper>