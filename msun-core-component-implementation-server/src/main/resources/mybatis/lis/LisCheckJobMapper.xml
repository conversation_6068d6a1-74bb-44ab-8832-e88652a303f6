<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.lis.dao.LisCheckJobMapper">

    <select id="getDerivativeNum" resultType="int">
        select count(*)
        from comm.lis_dict_derivative
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getWorkGroupNum" resultType="int">
        select count(*)
        from lis.lis_user_auth
        where work_group_list != ''
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getReportNum" resultType="int">
        select count(*)
        from lis.lis_report_main
        where (flag_print_cabinets + flag_print_lab + flag_print_nurse + flag_print_doc) > 0
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getSelfReportNum" resultType="int">
        select count(*)
        from lis.lis_report_main
        where flag_print_cabinets > 0
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getTriageCallNum" resultType="int">
        select count(*)
        from lis.lis_dict_queue_call_room
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getOneSignNum" resultType="int">
        select count(*)
        from lis.lis_oneself_sign_time
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getSensitiveNum" resultType="int">
        select count(*)
        from comm.lis_dict_lab_item
        where is_positive = 1
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getGetReportNum" resultType="int">
        select count(*)
        from lis.lis_dict_lab_vs_receipt
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getReagentNum" resultType="int">
        select count(*)
        from comm.dict_drug_basic_main
        where dict_input_type = '4'
          and delete_flag = '0'
          and hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

</mapper>