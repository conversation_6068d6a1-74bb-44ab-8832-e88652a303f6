<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.lis.dao.LisDeliveryMapper">

    <select id="getWorkGroup" resultType="int">
        select count(*)
        from lis.lis_work_group
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getSampleClass" resultType="int">
        select count(*)
        from comm.lis_dict_sample_class
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getReportType" resultType="int">
        select count(*)
        from comm.lis_dict_report_type
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getItem" resultType="int">
        select count(*)
        from comm.lis_dict_item
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getLabItem" resultType="int">
        select count(*)
        from comm.lis_dict_lab_item
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getEquipment" resultType="int">
        select count(*)
        from comm.lis_dict_equipment
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

    <select id="getOrderVsReport" resultType="int">
        select count(*)
        from comm.lis_dict_order_vs_report
        where hospital_id = #{hospitalId}
          and his_org_id = #{hisOrgId}
    </select>

</mapper>