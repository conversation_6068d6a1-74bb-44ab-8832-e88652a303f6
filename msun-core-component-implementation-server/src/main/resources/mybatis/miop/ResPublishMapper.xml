<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.ResPublishMapper">
    <sql id="baseSql">
        publish_id,
        res_id,
        publish_status,
        publish_type,
        publish_identity_id,
        publish_user_name,
        publlish_time,
        publish_trace_id,
        res_class,
        hospital_id,
        read_card_config,
        interface_field_config,
        fixmedins_default,
        company_name,
        company_ver,
        region,
        dict_type,
        mode_type,
        card_oper,
        param_path,
        interface_path,
        dll_path,
        res_hash,
        res_version,
        memo,
        provice_id,
        city_id
    </sql>
    <!--获取当前医院的资源包-->
    <select id="getCurrCompany" resultType="com.msun.core.component.implementation.api.imsp.vo.ResPublishVO">
        select
        <include refid="baseSql"/>
        from miop.res_publish
        where
        hospital_id = #{hospatilId}
        <if test="publishType != null and publishType != ''">
            and publish_type = #{publishType}
        </if>
        <if test="resClass != null and resClass != ''">
            and res_class = #{resClass}
        </if>
        <if test="publishStatus != null and publishStatus != ''">
            and publish_status = #{publishStatus}
        </if>
        order by his_update_time desc
        limit 1
    </select>
</mapper>