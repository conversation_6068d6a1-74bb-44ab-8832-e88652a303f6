<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.material.dao.MaterialStandardCatagoryDictMapper">

    <select id="selectMaterialStandCatagory" resultType="com.msun.core.component.implementation.server.material.entity.po.MaterialStandardCatagoryDictPO">
        SELECT standard_catagory_id,standard_catagory_name,
        standard_catagory_code
         FROM comm.dict_m_standard_catagory
         order by order_no
    </select>

</mapper>