<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.peis.mapper.OrderCheckVsEquipMapper">
    <sql id="Base_Column_List">
        order_check_vs_equip_id,
        peis_reg_info_id,
        peis_order_check_id,
        order_id,
        order_name,
        file_name,
        file_path,
        servers_id,
        servers_name,
        servers_path,
        file_type,
        invalid_flag,
        hospital_id,
        version,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_updater_name,
        his_update_time,
        seq
    </sql>
    <resultMap type="com.msun.core.component.implementation.api.peis.vo.OrderCheckVsEquipVO" id="OrderCheckVsEquipMap">
        <result property="orderCheckVsEquipId" column="order_check_vs_equip_id" jdbcType="BIGINT"/>
        <result property="peisRegInfoId" column="peis_reg_info_id" jdbcType="BIGINT"/>
        <result property="peisOrderCheckId" column="peis_order_check_id" jdbcType="BIGINT"/>
        <result property="orderId" column="order_id" jdbcType="BIGINT"/>
        <result property="orderName" column="order_name" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="serversId" column="servers_id" jdbcType="BIGINT"/>
        <result property="serversName" column="servers_name" jdbcType="VARCHAR"/>
        <result property="serversPath" column="servers_path" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="BIGINT"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="BIGINT"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="BIGINT"/>
        <result property="hisUpdaterName" column="his_updater_name" jdbcType="VARCHAR"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

<!--auto generated by MybatisCodeHelper on 2021-03-07-->
    <select id="findOrderCheckVsEquip" resultMap="OrderCheckVsEquipMap" parameterType="com.msun.core.component.implementation.api.peis.dto.OrderCheckVsEquipDTO">
        select
        <include refid="Base_Column_List"/>
        from peis.order_check_vs_equip
        where hospital_id=#{hospitalId,jdbcType=BIGINT}
        and his_org_id=#{hisOrgId,jdbcType=BIGINT}
        <if test="invalidFlag != null">
            and invalid_flag = #{invalidFlag,jdbcType=INTEGER}
        </if>
        <if test="orderCheckVsEquipId != null">
            and order_check_vs_equip_id = #{orderCheckVsEquipId,jdbcType=BIGINT}
        </if>
        <if test="peisRegInfoId != null">
            and peis_reg_info_id = #{peisRegInfoId,jdbcType=BIGINT}
        </if>
        <if test="peisOrderCheckId != null">
            and peis_order_check_id = #{peisOrderCheckId,jdbcType=BIGINT}
        </if>
        <if test="orderId != null">
            and order_id = #{orderId,jdbcType=BIGINT}
        </if>
        <if test="serversId != null">
            and servers_id = #{serversId,jdbcType=BIGINT}
        </if>
        <if test="fileType != null">
            and file_type = #{fileType,jdbcType=INTEGER}
        </if>
        ORDER BY seq ASC
    </select>
</mapper>