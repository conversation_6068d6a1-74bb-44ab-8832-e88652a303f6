<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.peis.mapper.PeisRoomMapper">
    <sql id="Base_Column_List">
        peis_room_id,
        peis_room_name,
        delete_flag,
        delete_doc_id,
        delete_doc_name,
        delete_time,
        invalid_flag,
        hospital_id,
        version,
        his_org_id,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_updater_name,
        his_update_time,
        peis_room_address
    </sql>
    <resultMap type="com.msun.core.component.implementation.api.peis.vo.PeisRoomVO" id="PeisRoomMap">
        <result property="peisRoomId" column="peis_room_id" jdbcType="BIGINT"/>
        <result property="peisRoomName" column="peis_room_name" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="deleteDocId" column="delete_doc_id" jdbcType="BIGINT"/>
        <result property="deleteDocName" column="delete_doc_name" jdbcType="VARCHAR"/>
        <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="BIGINT"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="BIGINT"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="BIGINT"/>
        <result property="hisUpdaterName" column="his_updater_name" jdbcType="VARCHAR"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="peisRoomAddress" column="peis_room_address" jdbcType="VARCHAR"/>
    </resultMap>

<!--auto generated by MybatisCodeHelper on 2021-03-07-->
    <select id="findPeisRoom" resultMap="PeisRoomMap">
        select
        <include refid="Base_Column_List"/>
        from peis.peis_room
        <where>
            invalid_flag = 0
            and delete_flag = 0
            <if test="hisOrgId != null">
                and his_org_id=#{hisOrgId,jdbcType=BIGINT}
            </if>
            <if test="hospitalId != null">
                and hospital_id=#{hospitalId,jdbcType=BIGINT}
            </if>
        </where>
        ORDER BY his_create_time DESC
    </select>
</mapper>
