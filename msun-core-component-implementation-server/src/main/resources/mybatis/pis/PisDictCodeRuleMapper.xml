<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictCodeRuleMapper">

    <resultMap type="com.msun.core.component.implementation.server.pis.entity.po.PisDictCodeRule"
               id="PisDictCodeRuleMap">
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="codeLength" column="code_length" jdbcType="INTEGER"/>
        <result property="additionChar" column="addition_char" jdbcType="VARCHAR"/>
        <result property="prefix" column="prefix" jdbcType="VARCHAR"/>
        <result property="prefixType" column="prefix_type" jdbcType="INTEGER"/>
        <result property="dateFormat" column="date_format" jdbcType="VARCHAR"/>
        <result property="serialLength" column="serial_length" jdbcType="INTEGER"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="refreshFlag" column="refresh_flag" jdbcType="VARCHAR"/>
        <result property="curYear" column="cur_year" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="initFlag" column="init_flag" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>