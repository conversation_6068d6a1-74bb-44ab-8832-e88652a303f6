<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictExamineExtendMapper">

    <resultMap type="com.msun.core.component.implementation.server.pis.entity.po.PisDictExamineExtend"
               id="PisDictExamineExtendMap">
        <result property="examineExtendId" column="examine_extend_id" jdbcType="INTEGER"/>
        <result property="examineId" column="examine_id" jdbcType="INTEGER"/>
        <result property="signType" column="sign_type" jdbcType="INTEGER"/>
        <result property="examineResultClass" column="examine_result_class" jdbcType="INTEGER"/>
        <result property="drawnExamineFlag" column="drawn_examine_flag" jdbcType="VARCHAR"/>
        <result property="tissueExamineFlag" column="tissue_examine_flag" jdbcType="VARCHAR"/>
        <result property="frozenExamineFlag" column="frozen_examine_flag" jdbcType="VARCHAR"/>
        <result property="codeRuleId" column="code_rule_id" jdbcType="INTEGER"/>
        <result property="processNo" column="process_no" jdbcType="INTEGER"/>
        <result property="applyName" column="apply_name" jdbcType="VARCHAR"/>
        <result property="applyStyleId" column="apply_style_id" jdbcType="INTEGER"/>
        <result property="applyPrintFile" column="apply_print_file" jdbcType="VARCHAR"/>
        <result property="reportName" column="report_name" jdbcType="VARCHAR"/>
        <result property="reportStyleId" column="report_style_id" jdbcType="INTEGER"/>
        <result property="reportPrintFile" column="report_print_file" jdbcType="VARCHAR"/>
        <result property="generalExamineId" column="general_examine_id" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="clinicalShowFlag" column="clinical_show_flag" jdbcType="VARCHAR"/>
        <result property="reportReadType" column="report_read_type" jdbcType="INTEGER"/>
        <result property="microImageCount" column="micro_image_count" jdbcType="INTEGER"/>
        <result property="dtImageCount" column="dt_image_count" jdbcType="INTEGER"/>
    </resultMap>


</mapper>