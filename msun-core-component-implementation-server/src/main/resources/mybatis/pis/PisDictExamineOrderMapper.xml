<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictExamineOrderMapper">

    <resultMap type="com.msun.core.component.implementation.server.pis.entity.po.PisDictExamineOrder"
               id="PisDictExamineOrderMap">
        <result property="examineOrderId" column="examine_order_id" jdbcType="INTEGER"/>
        <result property="examineId" column="examine_id" jdbcType="INTEGER"/>
        <result property="dictOrderId" column="dict_order_id" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="checkType" column="check_type" jdbcType="INTEGER"/>
    </resultMap>


</mapper>