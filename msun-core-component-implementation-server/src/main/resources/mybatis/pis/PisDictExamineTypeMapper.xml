<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictExamineTypeMapper">

    <resultMap type="com.msun.core.component.implementation.server.pis.entity.po.PisDictExamineType"
               id="PisDictExamineTypeMap">
        <result property="examineId" column="examine_id" jdbcType="INTEGER"/>
        <result property="examineCode" column="examine_code" jdbcType="VARCHAR"/>
        <result property="examineName" column="examine_name" jdbcType="VARCHAR"/>
        <result property="examineFullName" column="examine_full_name" jdbcType="VARCHAR"/>
        <result property="orderCategoryId" column="order_category_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="pathologyClassId" column="pathology_class_id" jdbcType="INTEGER"/>
        <result property="examineClassId" column="examine_class_id" jdbcType="INTEGER"/>
        <result property="initFlag" column="init_flag" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>