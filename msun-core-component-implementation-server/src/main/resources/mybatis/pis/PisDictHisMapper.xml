<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictHisMapper">


    <select id="findHisDept" resultType="com.msun.core.component.implementation.api.imsp.vo.DeptVO">
        select dept_id,dept_name from comm.dept
        where hospital_id = #{hospitalId} and invalid_flag ='0'
          <if test="deptIds != null and deptIds.size() > 0">
            and dept_id in
              <foreach collection="deptIds" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </if>
        <if test="likeInputStr != null and likeInputStr != ''">
            and (dept_name like CONCAT('%', #{likeInputStr}, '%') or lower(input_code) like CONCAT('%', #{likeInputStr}, '%') or lower(full_code) like CONCAT('%', #{likeInputStr}, '%'))
        </if>
        order by sort_order
        limit 100
    </select>

    <select id="findHisOrderDict" resultType="com.msun.core.component.implementation.api.imsp.vo.DictOrderVo">
        select a.dict_order_id ,a.order_code ,a.order_name ,a.price ,c.dept_name exe_dept_name
        from comm.dict_order a
        left join comm.dept c on a.exe_dept_id = c.dept_id
        where a.invalid_flag = '0' and a.hospital_id =#{hospitalId}
        <if test="deptId != null and deptId != -1">
            and a.exe_dept_id = #{deptId}
        </if>
        <if test="orderIds != null and orderIds.size() > 0">
            and a.dict_order_id in
            <foreach collection="orderIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="likeInputStr != null and likeInputStr != ''">
            and (a.order_name like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%') or lower(a.input_code) like CONCAT('%', #{likeInputStr}, '%'))
        </if>

        order by a.order_no
        <if test="orderIds == null or orderIds.size() == 0">
            limit 100
        </if>

    </select>

    <select id="findHisChargeDict"
            resultType="com.msun.core.component.implementation.api.pis.vo.DictNurseOrderVO">
        select a.dict_nurse_order_id
        from comm.dict_nurse_order a
        inner join comm.dict_order_doc_vs_nurse b on a.dict_nurse_order_id = b.dict_nurse_order_id
        where a.invalid_flag ='0' and a.hospital_id =#{hospitalId}
        and b.dict_order_id in
        <foreach collection="orderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>