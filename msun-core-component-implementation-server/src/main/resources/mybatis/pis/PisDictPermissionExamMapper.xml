<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.core.component.implementation.server.pis.dao.PisDictPermissionExamMapper">

    <resultMap type="com.msun.core.component.implementation.server.pis.entity.po.PisDictPermissionExam"
               id="PisDictPermissionExamMap">
        <result property="permissionExamId" column="permission_exam_id" jdbcType="INTEGER"/>
        <result property="examineId" column="examine_id" jdbcType="INTEGER"/>
        <result property="permissionId" column="permission_id" jdbcType="INTEGER"/>
        <result property="clinicReadFlag" column="clinic_read_flag" jdbcType="VARCHAR"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="INTEGER"/>
        <result property="hisOrgId" column="his_org_id" jdbcType="INTEGER"/>
        <result property="hisCreaterId" column="his_creater_id" jdbcType="INTEGER"/>
        <result property="hisCreaterName" column="his_creater_name" jdbcType="VARCHAR"/>
        <result property="hisCreateTime" column="his_create_time" jdbcType="TIMESTAMP"/>
        <result property="hisUpdaterId" column="his_updater_id" jdbcType="INTEGER"/>
        <result property="hisUpdateTime" column="his_update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="clinicPrintFlag" column="clinic_print_flag" jdbcType="VARCHAR"/>
        <result property="advanceFrozenFlag" column="advance_frozen_flag" jdbcType="VARCHAR"/>
        <result property="printPermission" column="print_permission" jdbcType="INTEGER"/>
    </resultMap>


</mapper>