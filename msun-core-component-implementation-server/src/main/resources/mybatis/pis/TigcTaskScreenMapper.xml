<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.imsp.dao.TigcTackScreenMapper">

    <select id="selectTigcScreen"
            resultType="com.msun.core.component.implementation.server.imsp.entity.po.FzScreen">
        SELECT
            screen_id,
            screen_category
        FROM
            tigc.fz_screen
        WHERE
            hospital_id =#{hospitalId}
    </select>

</mapper>