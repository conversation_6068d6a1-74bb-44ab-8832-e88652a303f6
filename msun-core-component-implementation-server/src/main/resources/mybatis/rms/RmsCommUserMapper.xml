<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.rms.dao.RmsUserMapper">
    <select id="selectCountRmsUser" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM comm.USER A INNER JOIN comm.IDENTITY B ON A.user_id=B.user_id WHERE A.invalid_flag='0' AND B.delete_flag='0' AND B.role_id IN (SELECT DISTINCT r.role_id FROM comm.menu M
                                                                                                                                                                                     INNER JOIN comm.role_vs_menu rvm ON M.menu_id = rvm.menu_id
                                                                                                                                                                                     INNER JOIN comm.ROLE r ON rvm.role_id = r.role_id
                                                                                                                                                  WHERE ( r.hospital_id = #{hospitalId} OR r.hospital_id = - 1 ) AND r.his_org_id=#{hisOrgId}
                                                                                                                                                    AND M.menu_code = 'rms'
                                                                                                                                                    AND M.invalid_flag = '0'
                                                                                                                                                    AND r.invalid_flag = '0')
    </select>
</mapper>
