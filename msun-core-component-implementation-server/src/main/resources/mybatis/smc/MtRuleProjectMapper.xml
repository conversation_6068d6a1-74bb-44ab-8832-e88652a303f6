<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.smc.dao.MtRuleProjectMapper">
    <select id="checkMtRuleProject" resultType="java.lang.Integer">
        select count(*) from smc.mt_rule_project where 1=1
        <if test="hospitalId != null and hospitalId != ''">
           and hospital_id = #{hospitalId}
        </if>
    </select>
</mapper>
