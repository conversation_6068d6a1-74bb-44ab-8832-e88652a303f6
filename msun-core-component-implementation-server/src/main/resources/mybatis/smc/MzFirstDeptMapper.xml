<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.smc.dao.MzFirstDeptMapper">
    <select id="checkMzFirstDept" resultType="java.lang.Integer">
        select count(*) from smc.mz_first_dept where del_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="findMzFirstDeptList"
            resultType="com.msun.core.component.implementation.server.smc.entity.vo.MzFirstDeptVO"
            parameterType="java.lang.Long">
        select
        dept_id as deptId,
        first_dept_id as firstDeptId,
        dept_name as deptName
        from smc.mz_dept where invalid_flag = '0'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="findDeptList" resultType="com.msun.core.component.implementation.server.smc.entity.vo.MzFirstDeptVO"
            parameterType="java.lang.Long">
        select
        dept_id as deptId,
        dept_name as deptName
        from smc.mz_dept where invalid_flag = '0' and category_id='1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
</mapper>
