<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.smc.dao.MzRequestChannelMapper">

    <select id="checkRequestChannel" resultType="java.lang.Integer">
        select count(*) from smc.mz_request_channel where enable_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}
        </if>
        and source_platform like concat('%ty%')
    </select>
    <select id="checkRequestChannelYn" resultType="java.lang.Integer">
        select count(*) from smc.mz_request_channel where enable_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}
        </if>
        and source_platform like concat('%yn%')
    </select>
    <select id="checkRequestChannelWx" resultType="java.lang.Integer">
        select count(*) from smc.mz_request_channel where enable_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}
        </if>
        and source_platform like concat('%wx%')
    </select>
</mapper>
