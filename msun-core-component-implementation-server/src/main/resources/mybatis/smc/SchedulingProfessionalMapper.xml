<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.smc.dao.SchedulingProfessionalMapper">

    <select id="checkSchedulingProfessional" resultType="java.lang.Integer">
        select count(*) from comm.scheduling_professional where invalid_flag = '0'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="schedulingProfessional"
            resultType="com.msun.core.component.implementation.server.smc.entity.dto.StaffSchedulingProfessionalDTO"
            parameterType="java.lang.Long">
        select
        professional_id as professionalId,
        professional_name  as professionalName,
        professional_code  as professionalCode
        from comm.scheduling_professional where invalid_flag = '0'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="schedulingDocProfessional"
            resultType="com.msun.core.component.implementation.server.smc.entity.vo.MzDocVsProfessionalVO"
            parameterType="java.lang.Long">
        select
        professional_id as professionalId,
        doc_id as docId
        from comm.scheduling_doc_vs_professional where 1=1
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>

    </select>
</mapper>
