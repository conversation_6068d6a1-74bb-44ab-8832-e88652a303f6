<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.core.component.implementation.server.smc.dao.SchedulingRecordMapper">

    <select id="checkSchedulingRecord" resultType="java.lang.Integer">
        select count(*) from comm.scheduling_record where del_flag = '0'
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="checkSchedulingTemplate" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from smc.mz_scheduling_template where enable_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="checkSourceTemplate" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from smc.mz_source_template where enable_flag = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="findSchedulingRecordList"
            resultType="com.msun.core.component.implementation.server.smc.entity.vo.MzSchedulingVO"
            parameterType="java.lang.Long">
        select
        scheduling_id as schedulingId,
         dept_id as deptId
        from comm.scheduling_record where del_flag = '0'
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="checkDeptSchedulingRecord" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from comm.scheduling_record where del_flag = '0'
        and schedule_type='2'
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="checkDoctorSchedulingRecord" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from comm.scheduling_record where del_flag = '0'
        and schedule_type='1'
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
    <select id="checkModitalSchedulingRecord" resultType="java.lang.Integer">
        select count(*) from comm.scheduling_record where del_flag = '0'
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
        and dept_id in
        <foreach collection="deptIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="checkLisSchedulingRecord" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from comm.scheduling_record
        where del_flag = '0'
        and source_type = #{sourceType}
        and work_date >= CURRENT_DATE
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
    </select>
</mapper>
