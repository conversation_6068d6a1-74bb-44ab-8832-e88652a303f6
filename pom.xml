<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>


    <modules>
        <module>msun-core-component-implementation-api</module>
        <module>msun-core-component-implementation-server</module>
    </modules>


    <parent>
        <groupId>com.msun.parent</groupId>
        <artifactId>msun-parent</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>
    <groupId>com.msun.core.component.implementation</groupId>
    <artifactId>msun-core-component-implementation</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>${project.artifactId}</name>
    <description>${project.artifactId}</description>
    <properties>

    </properties>
    <dependencies>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
